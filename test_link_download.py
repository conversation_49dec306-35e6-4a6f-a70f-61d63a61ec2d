#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试链接下载功能
"""

from lofter_downloader import LofterDownloader


def test_link_download():
    """测试通过链接下载文章"""
    downloader = LofterDownloader()
    
    # 加载cookies
    if not downloader.load_cookies():
        print("❌ 无法加载Cookie，请先设置Cookie")
        return
    
    # 测试您提供的链接
    test_url = "https://dmscdj.lofter.com/post/84bc89fd_2be5a7548?incantation=rzD5Hc6Y0MGF"
    
    print("🧪 测试链接下载功能")
    print("=" * 50)
    print(f"📝 测试链接: {test_url}")
    print()
    
    # 下载文章
    content = downloader.download_from_url(test_url)
    
    if content:
        print("\n✅ 下载成功!")
        print("📖 文章内容预览:")
        print("-" * 50)
        
        # 显示前500个字符
        preview = content[:500]
        print(preview)
        if len(content) > 500:
            print("...")
        
        print("-" * 50)
        print(f"📊 总长度: {len(content)} 字符")
        
        # 提取标题和作者
        lines = content.split('\n')
        title = "未知标题"
        author = "未知作者"
        
        for line in lines:
            if line.startswith('标题: '):
                title = line[3:].strip()
            elif line.startswith('作者: '):
                author = line[3:].strip()
        
        print(f"📝 标题: {title}")
        print(f"👤 作者: {author}")
        
        # 保存文章
        downloader.save_article(title, content, author, "test_downloads")
        print("💾 文章已保存到 test_downloads 目录")
        
    else:
        print("❌ 下载失败")


if __name__ == "__main__":
    test_link_download()
