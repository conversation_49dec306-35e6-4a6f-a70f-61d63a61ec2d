#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确查找目标文章
"""

from lofter_downloader import LofterDownloader


def find_exact_article():
    """精确查找目标文章"""
    downloader = LofterDownloader()
    
    # 加载cookies
    if not downloader.load_cookies():
        print("❌ 无法加载Cookie，请先设置Cookie")
        return
    
    print("🎯 精确查找目标文章")
    print("=" * 50)
    
    target_title = "小鲛人被王爷亲手送进男风馆后，他学会了用残缺的尾巴讨好所有人"
    target_id = "11783533896"  # 从链接解析出的文章ID
    
    print(f"🎯 目标标题: {target_title}")
    print(f"🆔 目标ID: {target_id}")
    
    # 博客信息
    blog_info = {
        'blogName': 'dmscdj',
        'blogId': '2226948605',
        'blogNickName': '村口做饭大厨'
    }
    
    print(f"👤 作者: {blog_info['blogNickName']}")
    
    # 获取全部文章
    print("\n📚 获取作者的全部文章...")
    posts = downloader.get_all_author_posts(blog_info)
    
    if not posts:
        print("❌ 无法获取文章列表")
        return
    
    print(f"✅ 获取到 {len(posts)} 篇文章")
    
    # 方法1：通过ID查找
    print(f"\n🔍 方法1: 通过ID查找 ({target_id})")
    target_post_by_id = None
    for i, post in enumerate(posts, 1):
        post_id = None
        if post.get('post') and isinstance(post.get('post'), dict):
            post_id = str(post['post'].get('id', ''))
        
        if post_id == target_id:
            target_post_by_id = post
            print(f"✅ 通过ID找到文章 (第{i}篇)")
            break
    
    if not target_post_by_id:
        print("❌ 通过ID未找到文章")
    
    # 方法2：通过标题模糊匹配
    print(f"\n🔍 方法2: 通过标题模糊匹配")
    target_keywords = ['小鲛人', '王爷', '男风馆', '残缺', '尾巴', '讨好']
    
    matches = []
    for i, post in enumerate(posts, 1):
        # 提取标题
        title = None
        if post.get('post') and isinstance(post.get('post'), dict):
            nested_post = post.get('post')
            title = nested_post.get('title') or nested_post.get('digest')
        
        if not title:
            title = post.get('title') or post.get('digest') or post.get('noticeLinkTitle')
        
        if title:
            title = downloader.clean_html(title).strip()
            
            # 检查关键词匹配
            matched_keywords = [keyword for keyword in target_keywords if keyword in title]
            match_score = len(matched_keywords)
            
            if match_score >= 3:  # 至少匹配3个关键词
                matches.append((i, post, title, matched_keywords, match_score))
                print(f"🎯 高匹配度文章 (第{i}篇): {title}")
                print(f"   匹配关键词: {matched_keywords} (得分: {match_score})")
    
    # 方法3：通过标题精确匹配
    print(f"\n🔍 方法3: 通过标题精确匹配")
    target_post_by_title = None
    for i, post in enumerate(posts, 1):
        # 提取标题
        title = None
        if post.get('post') and isinstance(post.get('post'), dict):
            nested_post = post.get('post')
            title = nested_post.get('title') or nested_post.get('digest')
        
        if not title:
            title = post.get('title') or post.get('digest') or post.get('noticeLinkTitle')
        
        if title:
            title = downloader.clean_html(title).strip()
            
            # 检查是否包含目标标题的主要部分
            if '小鲛人' in title and '王爷' in title and '男风馆' in title:
                target_post_by_title = post
                print(f"✅ 通过标题找到文章 (第{i}篇): {title}")
                break
    
    if not target_post_by_title:
        print("❌ 通过标题未找到精确匹配")
    
    # 选择最佳结果
    target_post = target_post_by_id or target_post_by_title
    if not target_post and matches:
        # 选择得分最高的匹配
        best_match = max(matches, key=lambda x: x[4])
        target_post = best_match[1]
        print(f"✅ 选择最佳匹配: {best_match[2]}")
    
    if target_post:
        print(f"\n📖 开始下载文章...")
        
        # 提取文章信息
        if target_post.get('post') and isinstance(target_post.get('post'), dict):
            nested_post = target_post.get('post')
            title = nested_post.get('title') or nested_post.get('digest') or '未知标题'
            post_id = nested_post.get('id')
            permalink = nested_post.get('permalink')
            
            print(f"📝 文章标题: {title}")
            print(f"🆔 文章ID: {post_id}")
            print(f"🔗 永久链接: {permalink}")
        
        # 下载文章内容
        content = downloader.get_post_content(target_post)
        
        if content:
            print("✅ 下载成功!")
            print(f"📊 内容长度: {len(content)} 字符")
            
            # 显示内容预览
            print("\n📖 内容预览:")
            print("-" * 50)
            lines = content.split('\n')
            for line in lines[:10]:  # 显示前10行
                if line.strip():
                    print(line.strip())
            print("...")
            print("-" * 50)
            
            # 保存文章
            final_title = title if 'title' in locals() else target_title
            downloader.save_article(final_title, content, blog_info['blogNickName'], "exact_downloads")
            print("💾 文章已保存到 exact_downloads 目录")
        else:
            print("❌ 下载失败")
    else:
        print("❌ 未找到目标文章")
        print("💡 建议:")
        print("   1. 检查文章是否仍然存在")
        print("   2. 确认Cookie是否有效")
        print("   3. 尝试手动浏览作者页面确认文章")


if __name__ == "__main__":
    find_exact_article()
