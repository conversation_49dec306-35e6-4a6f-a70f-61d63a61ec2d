#!/usr/bin/env python3
# _*_ coding:utf-8 _*_

import os
import sys

# 设置环境变量（确保它们存在）
os.environ["NAPCAT_URL"] = "http://**************:7765/send_group_msg"
os.environ["NAPCAT_GROUP_ID"] = "1027740020"

print("=== 环境变量设置 ===")
print(f"NAPCAT_URL: {os.getenv('NAPCAT_URL')}")
print(f"NAPCAT_GROUP_ID: {os.getenv('NAPCAT_GROUP_ID')}")

# 导入notify模块
sys.path.append('.')
import importlib.util

# 查找notify文件
notify_file = None
possible_paths = [
    "notify(1).py",
    "notify.py",
    "../zhongyi/notify(1).py",
    "/ql/data/scripts/notify(1).py",
    "/ql/data/scripts/notify.py"
]

for path in possible_paths:
    if os.path.exists(path):
        notify_file = path
        break

if not notify_file:
    print("找不到notify文件，尝试的路径:")
    for path in possible_paths:
        print(f"  - {path}")
    sys.exit(1)

print(f"找到notify文件: {notify_file}")

try:
    spec = importlib.util.spec_from_file_location("notify", notify_file)
    notify_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(notify_module)
    print("成功导入notify模块")
except Exception as e:
    print(f"导入失败: {e}")
    sys.exit(1)

# 检查push_config
print("\n=== push_config 检查 ===")
print(f"NAPCAT_URL in push_config: {notify_module.push_config.get('NAPCAT_URL')}")
print(f"NAPCAT_GROUP_ID in push_config: {notify_module.push_config.get('NAPCAT_GROUP_ID')}")

# 检查推送函数列表
print("\n=== 推送函数列表 ===")
notify_functions = notify_module.add_notify_function()
function_names = [func.__name__ for func in notify_functions]
print(f"推送函数: {function_names}")
print(f"napcat 是否在列表中: {'napcat' in function_names}")

# 如果napcat不在列表中，手动检查条件
if 'napcat' not in function_names:
    print("\n=== 调试napcat未被添加的原因 ===")
    napcat_url = notify_module.push_config.get("NAPCAT_URL")
    napcat_group_id = notify_module.push_config.get("NAPCAT_GROUP_ID")
    print(f"NAPCAT_URL 值: '{napcat_url}' (类型: {type(napcat_url)})")
    print(f"NAPCAT_GROUP_ID 值: '{napcat_group_id}' (类型: {type(napcat_group_id)})")
    print(f"条件1 - NAPCAT_URL 存在: {bool(napcat_url)}")
    print(f"条件2 - NAPCAT_GROUP_ID 存在: {bool(napcat_group_id)}")
    print(f"两个条件都满足: {bool(napcat_url) and bool(napcat_group_id)}")

# 直接测试napcat函数
print("\n=== 直接测试napcat函数 ===")
try:
    notify_module.napcat("测试标题", "这是一条测试消息")
except Exception as e:
    print(f"napcat函数调用失败: {e}")

print("\n=== 测试完成 ===")
