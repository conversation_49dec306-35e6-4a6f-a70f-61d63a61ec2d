#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试标题匹配功能
"""

from lofter_downloader import LofterDownloader


def test_title_matching():
    """测试标题匹配功能"""
    downloader = LofterDownloader()
    
    # 加载cookies
    if not downloader.load_cookies():
        print("❌ 无法加载Cookie，请先设置Cookie")
        return
    
    print("🧪 测试标题匹配功能")
    print("=" * 50)
    
    # 模拟博客信息
    blog_info = {
        'blogName': 'dmscdj',
        'blogId': '2226948605',
        'blogNickName': '村口做饭大厨'
    }
    
    print(f"👤 测试作者: {blog_info['blogNickName']}")
    
    # 获取文章列表
    print("📚 获取文章列表...")
    posts = downloader.get_author_posts(blog_info)
    
    if not posts:
        print("❌ 无法获取文章列表")
        return
    
    print(f"✅ 获取到 {len(posts)} 篇文章")
    
    # 测试标题匹配
    print("\n🔍 开始标题匹配测试...")
    target_post = downloader._find_post_by_title(posts)
    
    if target_post:
        print(f"✅ 匹配成功!")
        title = target_post.get('title', target_post.get('digest', '无标题'))
        print(f"📖 匹配的文章: {title}")
        
        # 尝试获取文章内容
        print("\n📖 获取文章内容...")
        content = downloader.get_post_content(target_post)
        
        if content:
            print("✅ 内容获取成功!")
            print(f"📊 内容长度: {len(content)} 字符")
            print("📖 内容预览:")
            print("-" * 50)
            print(content[:300] + "..." if len(content) > 300 else content)
            print("-" * 50)
            
            # 保存文章
            downloader.save_article(title, content, blog_info['blogNickName'], "test_downloads")
            print("💾 文章已保存到 test_downloads 目录")
        else:
            print("❌ 内容获取失败")
    else:
        print("❌ 标题匹配失败")


if __name__ == "__main__":
    test_title_matching()
