# Lofter下载器使用示例

## 快速开始

### 1. 环境准备

确保已安装Python 3.7+：
```bash
python --version
```

安装依赖：
```bash
pip install -r requirements.txt
```

### 2. 获取Cookie

#### 方法一：浏览器开发者工具
1. 打开浏览器，访问 https://newsmiss.lofter.com
2. 登录你的账号
3. 按F12打开开发者工具
4. 切换到Network（网络）标签页
5. 刷新页面
6. 找到任意一个请求，查看Request Headers
7. 复制Cookie头的完整值

#### 方法二：浏览器插件
使用Cookie导出插件（如EditThisCookie）导出Cookie

### 3. 设置Cookie

#### 方法一：创建cookies.txt文件
```
LOFTER-XXXXXX-LOGIN-AUTH=xxxxxxxxxxxxxxx; other_cookie=value; session_id=xxxxx
```

#### 方法二：程序运行时输入
运行程序后选择"直接输入Cookie值"选项

### 4. 运行程序

#### Windows用户
双击 `start_downloader.bat` 文件

#### 其他系统
```bash
python lofter_downloader.py
```

## 使用示例

### 示例1：搜索并下载单篇文章

```
🌟 Lofter文章下载器
============================================================
✅ Cookies加载成功
🔑 使用认证: LOFTER-XXXXXX-LOGIN-AUTH

==================================================
📋 主菜单
==================================================
1. 🔍 搜索作者
2. 📖 测试功能
3. ❓ 帮助信息
4. 🚪 退出程序

请选择操作 (1-4): 1

🔍 作者搜索
==============================
💡 提示: 可以搜索作者的昵称或用户名
📝 示例: 村口做饭大厨

请输入作者名: 村口做饭大厨

🔍 搜索作者: 村口做饭大厨
📡 请求URL: https://api.lofter.com/newsearch/blog.json?key=%E6%9D%91%E5%8F%A3%E5%81%9A%E9%A5%AD%E5%A4%A7%E5%8E%A8&limit=10&offset=0
📊 响应状态码: 200
📋 响应数据结构: ['code', 'data', 'msg']
✅ 找到 1 个匹配的博客

✅ 找到 1 个匹配的作者:
----------------------------------------
1. 村口做饭大厨
   用户名: @xxxxx
   简介: 专注于创作优质内容...

请选择作者 (输入序号): 1

📚 正在获取 村口做饭大厨 的文章列表...
🆔 博客ID: 123456789
📝 博客名: xxxxx
📡 请求数据: {'targetblogid': '123456789', 'supportposttypes': '1,2,3,4,5,6', ...}
📊 响应状态码: 200
📋 响应数据结构: ['response']
✅ 找到 25 篇文章

📖 村口做饭大厨 的文章列表 (共25篇):
============================================================
  1. ♂♂小鲛人被王爷亲手送进男风馆后，他学会了用残缺的尾巴讨好所有人
  2. 另一篇文章标题...
  3. 第三篇文章标题...
  ...
 25. 最后一篇文章标题

 26. 📦 下载全部文章
 27. 🔙 返回主菜单

请选择要下载的文章 (1-27): 1

📖 下载文章: ♂♂小鲛人被王爷亲手送进男风馆后，他学会了用残缺的尾巴讨好所有人
⏳ 正在获取文章内容...
📖 获取文章内容: ♂♂小鲛人被王爷亲手送进男风馆后，他学会了用残缺的尾巴讨好所有人...
🆔 文章ID: 987654321
🆔 博客ID: 123456789
📡 请求数据: blogdomain=_blogid_123456789.lofter.com&postid=987654321
📊 响应状态码: 200
📋 响应数据结构: ['response']
✅ 成功获取文章内容 (2500 字符)
✅ 文章已保存: downloads\村口做饭大厨_♂♂小鲛人被王爷亲手送进男风馆后，他学会了用残缺的尾巴讨好所有人.txt
✅ 文章下载完成!

是否继续下载其他文章? (y/n): n
```

### 示例2：批量下载作者所有文章

```
请选择要下载的文章 (1-27): 26

📦 开始批量下载 25 篇文章...
⏳ 请耐心等待，避免请求过于频繁...

[1/25] 下载: ♂♂小鲛人被王爷亲手送进男风馆后，他学会了用残缺的尾巴讨好所有人...
📖 获取文章内容: ♂♂小鲛人被王爷亲手送进男风馆后，他学会了用残缺的尾巴讨好所有人...
✅ 成功获取文章内容 (2500 字符)
✅ 文章已保存: downloads\村口做饭大厨_文章1.txt
✅ 成功

[2/25] 下载: 第二篇文章标题...
📖 获取文章内容: 第二篇文章标题...
✅ 成功获取文章内容 (1800 字符)
✅ 文章已保存: downloads\村口做饭大厨_文章2.txt
✅ 成功

...

[25/25] 下载: 最后一篇文章标题...
✅ 成功

🎉 批量下载完成!
📊 成功: 25/25 篇文章
```

## 常见问题

### Q1: Cookie认证失败
**症状**: 显示"❌ 认证失败，请检查Cookie是否有效"

**解决方案**:
1. 重新获取Cookie（Cookie可能已过期）
2. 确保Cookie包含`LOFTER-XXXXXX-LOGIN-AUTH`认证信息
3. 检查Cookie格式是否正确

### Q2: 搜索不到作者
**症状**: 显示"❌ 未找到该作者"

**解决方案**:
1. 检查作者名拼写是否正确
2. 尝试使用作者的完整昵称
3. 确保网络连接正常
4. 该作者可能设置了隐私保护

### Q3: 文章内容为空
**症状**: 显示"⚠️ 文章内容为空，可能需要特殊权限访问"

**解决方案**:
1. 该文章可能需要特殊权限访问
2. 文章可能已被删除
3. 检查Cookie是否有足够的访问权限

### Q4: 下载速度慢
**症状**: 批量下载时速度很慢

**说明**: 这是正常现象，程序会自动控制请求频率以避免被服务器封禁

## 文件结构说明

```
lofter_downloader/
├── lofter_downloader.py      # 主程序
├── test_downloader.py        # 测试脚本
├── start_downloader.bat      # Windows启动脚本
├── requirements.txt          # 依赖包列表
├── config.json              # 配置文件
├── README.md                # 使用说明
├── USAGE_EXAMPLES.md        # 使用示例（本文件）
├── cookies.txt              # Cookie文件（需要手动创建）
├── cookies_template.txt     # Cookie模板文件
└── downloads/               # 下载的文章保存目录
    ├── 村口做饭大厨_文章1.txt
    ├── 村口做饭大厨_文章2.txt
    └── ...
```

## 高级用法

### 自定义配置
编辑 `config.json` 文件可以修改：
- 下载目录
- 请求延时
- 文件命名格式
- API端点等

### 测试功能
运行测试脚本验证功能：
```bash
python test_downloader.py
```

### 调试模式
在 `config.json` 中设置 `"enable_debug": true` 可以看到更详细的调试信息
