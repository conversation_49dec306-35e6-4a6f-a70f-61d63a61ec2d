#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lofter文章下载器
基于提供的JSON书源配置实现的控制台下载工具
支持通过Cookie登录访问，按作者搜索和下载文章
"""

import requests
import json
import os
import re
import time
from urllib.parse import quote, unquote
from typing import Dict, List, Optional, Tuple
import html


class LofterDownloader:
    def __init__(self):
        self.session = requests.Session()
        self.cookies = {}
        self.headers = {
            "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        self.base_url = "https://newsmiss.lofter.com"
        self.search_base_url = "https://api.lofter.com/newsearch/"
        
    def load_cookies(self, cookie_file: str = "cookies.txt") -> bool:
        """从文件加载cookies"""
        try:
            if os.path.exists(cookie_file):
                with open(cookie_file, 'r', encoding='utf-8') as f:
                    cookie_str = f.read().strip()

                # 跳过注释行和空行
                lines = cookie_str.split('\n')
                cookie_str = ""
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        cookie_str = line
                        break

                if not cookie_str:
                    print("❌ Cookie文件中没有找到有效的Cookie值")
                    return False

                # 解析cookie字符串
                for cookie in cookie_str.split(';'):
                    if '=' in cookie:
                        key, value = cookie.strip().split('=', 1)
                        self.cookies[key] = value

                # 查找LOFTER登录认证cookie
                lofter_auth = None
                for key, value in self.cookies.items():
                    if key.startswith('LOFTER-') and key.endswith('-LOGIN-AUTH'):
                        lofter_auth = {key: value}
                        break

                if lofter_auth:
                    self.headers.update(lofter_auth)
                    self.session.cookies.update(self.cookies)
                    print("✅ Cookies加载成功")
                    print(f"🔑 使用认证: {list(lofter_auth.keys())[0]}")
                    return True
                else:
                    print("❌ 未找到有效的LOFTER登录认证cookie")
                    print("💡 请确保Cookie中包含类似 'LOFTER-XXXXXX-LOGIN-AUTH' 的认证信息")
                    return False
            else:
                print(f"❌ Cookie文件 {cookie_file} 不存在")
                return False
        except Exception as e:
            print(f"❌ 加载cookies失败: {e}")
            return False
    
    def save_cookies(self, cookie_str: str, cookie_file: str = "cookies.txt"):
        """保存cookies到文件"""
        try:
            with open(cookie_file, 'w', encoding='utf-8') as f:
                f.write(cookie_str)
            print(f"✅ Cookies已保存到 {cookie_file}")
        except Exception as e:
            print(f"❌ 保存cookies失败: {e}")
    
    def search_author(self, author_name: str, page: int = 1) -> Optional[Dict]:
        """搜索作者，返回作者信息和文章列表"""
        try:
            # 根据JSON配置构建搜索URL
            offset = (page - 1) * 10
            search_url = f"{self.search_base_url}blog.json?key={quote(author_name)}&limit=10&offset={offset}"

            print(f"🔍 搜索作者: {author_name}")
            print(f"📡 请求URL: {search_url}")

            response = self.session.get(search_url, headers=self.headers)

            print(f"📊 响应状态码: {response.status_code}")

            if response.status_code == 401:
                print("❌ 认证失败，请检查Cookie是否有效")
                return None
            elif response.status_code == 403:
                print("❌ 访问被拒绝，可能需要重新登录")
                return None

            response.raise_for_status()

            try:
                data = response.json()
            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON格式")
                print(f"响应内容: {response.text[:200]}...")
                return None

            print(f"📋 响应数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")

            if 'data' in data and 'blogs' in data['data'] and data['data']['blogs']:
                blogs_count = len(data['data']['blogs'])
                print(f"✅ 找到 {blogs_count} 个匹配的博客")
                return data['data']
            else:
                print("❌ 未找到该作者")
                if 'data' in data:
                    print(f"💡 搜索返回的数据结构: {list(data['data'].keys()) if isinstance(data['data'], dict) else data['data']}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return None
        except Exception as e:
            print(f"❌ 搜索作者失败: {e}")
            return None
    
    def get_author_posts(self, blog_info: Dict) -> Optional[List[Dict]]:
        """获取作者的所有文章列表"""
        try:
            blog_id = blog_info.get('blogId')
            blog_name = blog_info.get('blogName')

            if not blog_id or not blog_name:
                print("❌ 缺少必要的博客信息")
                print(f"💡 博客信息: {blog_info}")
                return None

            print(f"📚 获取作者文章列表...")
            print(f"🆔 博客ID: {blog_id}")
            print(f"📝 博客名: {blog_name}")

            # 根据JSON配置构建获取文章列表的URL
            api_url = "http://api.lofter.com/v2.0/blogHomePage.api?product=lofter-android-7.4.4"

            post_data = {
                "targetblogid": blog_id,
                "supportposttypes": "1,2,3,4,5,6",
                "blogdomain": f"{blog_name}.lofter.com",
                "offset": "0",
                "method": "getPostLists",
                "postdigestnew": "1",
                "returnData": "1",
                "limit": "500",  # 获取更多文章
                "checkpwd": "1",
                "needgetpoststat": "1"
            }

            print(f"� 请求数据: {post_data}")

            response = self.session.post(api_url, data=post_data, headers=self.headers)

            print(f"📊 响应状态码: {response.status_code}")

            if response.status_code == 401:
                print("❌ 认证失败，请检查Cookie是否有效")
                return None
            elif response.status_code == 403:
                print("❌ 访问被拒绝，可能需要重新登录")
                return None

            response.raise_for_status()

            try:
                data = response.json()
            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON格式")
                print(f"响应内容: {response.text[:200]}...")
                return None

            print(f"📋 响应数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")

            if 'response' in data and 'posts' in data['response']:
                posts = data['response']['posts']
                print(f"✅ 找到 {len(posts)} 篇文章")
                return posts
            else:
                print("❌ 获取文章列表失败")
                if 'response' in data:
                    print(f"💡 响应结构: {list(data['response'].keys()) if isinstance(data['response'], dict) else data['response']}")
                else:
                    print(f"💡 完整响应: {data}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return None
        except Exception as e:
            print(f"❌ 获取文章列表失败: {e}")
            return None

    def get_all_author_posts(self, blog_info: Dict) -> Optional[List[Dict]]:
        """获取作者的所有文章列表（分页获取全部文章）"""
        try:
            blog_id = blog_info.get('blogId')
            blog_name = blog_info.get('blogName')

            if not blog_id or not blog_name:
                print("❌ 缺少必要的博客信息")
                return None

            print(f"📚 开始获取作者的全部文章...")
            print(f"🆔 博客ID: {blog_id}")
            print(f"📝 博客名: {blog_name}")

            all_posts = []
            offset = 0
            limit = 500
            page = 1

            while True:
                print(f"📄 正在获取第 {page} 页文章 (偏移量: {offset})...")

                # 根据JSON配置构建获取文章列表的URL
                api_url = "http://api.lofter.com/v2.0/blogHomePage.api?product=lofter-android-7.4.4"

                post_data = {
                    "targetblogid": blog_id,
                    "supportposttypes": "1,2,3,4,5,6",
                    "blogdomain": f"{blog_name}.lofter.com",
                    "offset": str(offset),
                    "method": "getPostLists",
                    "postdigestnew": "1",
                    "returnData": "1",
                    "limit": str(limit),
                    "checkpwd": "1",
                    "needgetpoststat": "1"
                }

                response = self.session.post(api_url, data=post_data, headers=self.headers)

                if response.status_code == 401:
                    print("❌ 认证失败，请检查Cookie是否有效")
                    return None
                elif response.status_code == 403:
                    print("❌ 访问被拒绝，可能需要重新登录")
                    return None

                response.raise_for_status()

                try:
                    data = response.json()
                except json.JSONDecodeError:
                    print("❌ 响应不是有效的JSON格式")
                    return None

                if 'response' in data and 'posts' in data['response']:
                    posts = data['response']['posts']

                    if not posts:
                        # 没有更多文章了
                        print(f"📄 第 {page} 页没有更多文章")
                        break

                    all_posts.extend(posts)
                    print(f"✅ 第 {page} 页获取到 {len(posts)} 篇文章，累计 {len(all_posts)} 篇")

                    # 如果这一页的文章数量少于limit，说明已经是最后一页
                    if len(posts) < limit:
                        print(f"📄 已获取完所有文章（最后一页只有 {len(posts)} 篇）")
                        break

                    # 准备获取下一页
                    offset += limit
                    page += 1

                    # 移除延时，加快下载速度
                    # time.sleep(1)
                else:
                    print("❌ 获取文章列表失败")
                    break

            if all_posts:
                print(f"🎉 总共获取到 {len(all_posts)} 篇文章")
                return all_posts
            else:
                print("❌ 未获取到任何文章")
                return None

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return None
        except Exception as e:
            print(f"❌ 获取文章列表失败: {e}")
            return None

    def get_post_content(self, post_info: Dict) -> Optional[str]:
        """获取单篇文章内容"""
        try:
            # 处理嵌套的post结构
            if 'post' in post_info and isinstance(post_info['post'], dict):
                actual_post = post_info['post']
                blog_id = actual_post.get('blogId')
                post_id = actual_post.get('id')
                title = actual_post.get('title', actual_post.get('digest', '无标题'))

                # 如果已经有完整的content，直接使用
                if actual_post.get('content'):
                    content = actual_post.get('content')
                    author = actual_post.get('blogInfo', {}).get('blogNickName', '未知作者')
                    publish_time = actual_post.get('publishTime', '')

                    if publish_time:
                        try:
                            import datetime
                            publish_time = datetime.datetime.fromtimestamp(int(publish_time)/1000).strftime('%Y-%m-%d %H:%M:%S')
                        except:
                            pass

                    # 清理HTML标签
                    content = self.clean_html(content)

                    if not content.strip():
                        print("⚠️ 文章内容为空")
                        return None

                    result = f"标题: {title}\n作者: {author}\n"
                    if publish_time:
                        result += f"发布时间: {publish_time}\n"
                    result += f"\n{'-'*50}\n\n{content}"

                    print(f"✅ 成功获取文章内容 ({len(content)} 字符)")
                    return result
            else:
                # 处理简单的post结构
                blog_id = post_info.get('blogId')
                post_id = post_info.get('id')
                title = post_info.get('title', post_info.get('digest', '无标题'))

            if not blog_id or not post_id:
                print("❌ 缺少文章信息")
                print(f"💡 文章信息: {list(post_info.keys())}")
                return None

            print(f"📖 获取文章内容: {title[:30]}...")
            print(f"🆔 文章ID: {post_id}")
            print(f"🆔 博客ID: {blog_id}")

            # 根据JSON配置构建获取文章详情的URL
            api_url = "https://api.lofter.com/oldapi/post/detail.api?product=lofter-android-7.4.4"

            post_data = f"blogdomain=_blogid_{blog_id}.lofter.com&postid={post_id}"

            print(f"📡 请求数据: {post_data}")

            response = self.session.post(api_url, data=post_data, headers=self.headers)

            print(f"📊 响应状态码: {response.status_code}")

            if response.status_code == 401:
                print("❌ 认证失败，请检查Cookie是否有效")
                return None
            elif response.status_code == 403:
                print("❌ 访问被拒绝，可能需要重新登录")
                return None

            response.raise_for_status()

            try:
                data = response.json()
            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON格式")
                print(f"响应内容: {response.text[:200]}...")
                return None

            print(f"📋 响应数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")

            if 'response' in data and 'post' in data['response']:
                post_data = data['response']['post']

                # 提取文章内容
                content = post_data.get('content', '')
                title = post_data.get('title', post_data.get('digest', '无标题'))
                author = post_data.get('blogInfo', {}).get('blogNickName', '未知作者')
                publish_time = post_data.get('publishTime', '')

                if publish_time:
                    try:
                        import datetime
                        publish_time = datetime.datetime.fromtimestamp(int(publish_time)/1000).strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        pass

                # 清理HTML标签
                content = self.clean_html(content)

                if not content.strip():
                    print("⚠️ 文章内容为空，可能需要特殊权限访问")
                    return None

                result = f"标题: {title}\n作者: {author}\n"
                if publish_time:
                    result += f"发布时间: {publish_time}\n"
                result += f"\n{'-'*50}\n\n{content}"

                print(f"✅ 成功获取文章内容 ({len(content)} 字符)")
                return result
            else:
                print("❌ 获取文章内容失败")
                if 'response' in data:
                    print(f"💡 响应结构: {list(data['response'].keys()) if isinstance(data['response'], dict) else data['response']}")
                else:
                    print(f"💡 完整响应: {data}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return None
        except Exception as e:
            print(f"❌ 获取文章内容失败: {e}")
            return None

    def download_from_url(self, article_url: str) -> Optional[str]:
        """智能链接下载：遵循JSON逻辑，通过作者搜索找到目标文章"""
        try:
            print(f"🔗 智能下载文章链接: {article_url}")

            # 解析URL获取博客信息
            # URL格式: https://dmscdj.lofter.com/post/84bc89fd_2be5a7548?incantation=rzD5Hc6Y0MGF
            url_pattern = r'https://([^.]+)\.lofter\.com/post/([^_]+)_([^?]+)'
            match = re.match(url_pattern, article_url)

            if not match:
                print("❌ 无法解析文章链接格式")
                return None

            blog_name = match.group(1)
            blog_id_hex = match.group(2)
            post_id_hex = match.group(3)

            print(f"📝 博客名: {blog_name}")
            print(f"🆔 博客ID (hex): {blog_id_hex}")
            print(f"📄 文章ID (hex): {post_id_hex}")

            # 将16进制ID转换为10进制
            try:
                blog_id = str(int(blog_id_hex, 16))
                post_id = str(int(post_id_hex, 16))
                print(f"🆔 博客ID (dec): {blog_id}")
                print(f"📄 文章ID (dec): {post_id}")
            except ValueError:
                print("❌ ID转换失败")
                return None

            # 方案1：先尝试直接API获取
            print("\n📡 方案1: 尝试直接API获取文章...")
            direct_result = self._try_direct_api(blog_id, post_id, article_url)
            if direct_result:
                return direct_result

            # 方案2：通过作者搜索获取（遵循JSON逻辑）
            print("\n🔍 方案2: 通过作者搜索获取文章（遵循JSON逻辑）...")

            # 获取博客信息
            blog_info = self._get_blog_info_by_name(blog_name, blog_id)
            if not blog_info:
                return None

            author_name = blog_info.get('blogNickName', '未知作者')
            print(f"👤 找到作者: {author_name}")

            # 获取作者的全部文章
            print(f"📚 获取作者 {author_name} 的全部文章...")
            posts = self.get_all_author_posts(blog_info)
            if not posts:
                print("❌ 无法获取作者文章列表")
                return None

            # 查找目标文章
            print(f"🔍 在 {len(posts)} 篇文章中查找目标文章...")
            target_post = None

            # 方法1：通过ID查找
            for post in posts:
                if post.get('post') and isinstance(post.get('post'), dict):
                    if str(post['post'].get('id', '')) == post_id:
                        target_post = post
                        print(f"✅ 通过ID找到目标文章")
                        break

            # 方法2：通过标题匹配
            if not target_post:
                print("💡 ID匹配失败，尝试标题匹配...")
                keywords = ['小鲛人', '王爷', '男风馆', '残缺', '尾巴', '讨好']

                best_match = None
                best_score = 0

                for post in posts:
                    # 提取标题
                    title = None
                    if post.get('post') and isinstance(post.get('post'), dict):
                        nested_post = post.get('post')
                        title = nested_post.get('title') or nested_post.get('digest')

                    if title:
                        title = self.clean_html(title).strip()

                        # 检查关键词匹配
                        matched_keywords = [keyword for keyword in keywords if keyword in title]
                        match_score = len(matched_keywords)

                        if match_score >= 3:  # 至少匹配3个关键词
                            if match_score > best_score:
                                best_score = match_score
                                best_match = post
                                print(f"🎯 找到匹配文章: {title}")
                                print(f"   匹配关键词: {matched_keywords} (得分: {match_score})")

                if best_match:
                    target_post = best_match
                    print(f"✅ 选择最佳匹配文章")

            if target_post:
                print(f"📖 开始下载文章内容...")
                return self.get_post_content(target_post)
            else:
                print("❌ 未找到目标文章")
                return None

        except Exception as e:
            print(f"❌ 智能下载失败: {e}")
            return None

    def _try_direct_api(self, blog_id: str, post_id: str, article_url: str) -> Optional[str]:
        """尝试直接API获取文章"""
        try:
            api_url = "https://api.lofter.com/oldapi/post/detail.api?product=lofter-android-7.4.4"
            post_data = f"blogdomain=_blogid_{blog_id}.lofter.com&postid={post_id}"

            response = self.session.post(api_url, data=post_data, headers=self.headers)

            if response.status_code != 200:
                print(f"📊 直接API响应状态码: {response.status_code}")
                return None

            data = response.json()

            if 'response' in data and 'post' in data['response']:
                post_data = data['response']['post']
                return self._extract_article_content(post_data, article_url)
            else:
                print("📋 直接API未返回文章数据")
                return None

        except Exception as e:
            print(f"📋 直接API获取失败: {e}")
            return None




    def _get_blog_info_by_name(self, blog_name: str, blog_id: str) -> Optional[Dict]:
        """通过博客名获取博客信息"""
        try:
            # 构造博客信息（模拟搜索结果）
            blog_info = {
                'blogName': blog_name,
                'blogId': blog_id,
                'blogNickName': '未知作者'  # 这个会在获取文章列表时更新
            }

            # 尝试通过API获取更详细的博客信息
            api_url = "http://api.lofter.com/v2.0/blogHomePage.api?product=lofter-android-7.4.4"
            post_data = {
                "targetblogid": blog_id,
                "method": "getBlogInfoDetail",
                "returnData": "1",
                "checkpwd": "1",
                "needgetpoststat": "1"
            }

            response = self.session.post(api_url, data=post_data, headers=self.headers)
            if response.status_code == 200:
                data = response.json()
                if 'response' in data and 'blogInfo' in data['response']:
                    blog_detail = data['response']['blogInfo']
                    blog_info['blogNickName'] = blog_detail.get('blogNickName', '未知作者')
                    print(f"✅ 获取到博客详细信息: {blog_info['blogNickName']}")

            return blog_info

        except Exception as e:
            print(f"❌ 获取博客信息失败: {e}")
            return None

    def _find_post_by_title_old(self, posts: List[Dict]) -> Optional[Dict]:
        """通过标题匹配查找文章"""
        try:
            print("\n📚 显示文章列表进行标题匹配:")
            print("=" * 60)

            # 调试：显示第一篇文章的完整数据结构
            if posts:
                print("🔍 调试信息 - 第一篇文章的数据结构:")
                first_post = posts[0]
                print(f"   可用字段: {list(first_post.keys())}")
                if 'post' in first_post:
                    nested_post = first_post['post']
                    print(f"   嵌套post字段: {list(nested_post.keys()) if isinstance(nested_post, dict) else type(nested_post)}")
                print()

            valid_posts = []
            for i, post in enumerate(posts, 1):
                # 尝试多种方式获取标题，包括嵌套的post对象
                title = None

                # 方式1：直接从post对象获取
                if post.get('title'):
                    title = post.get('title')
                elif post.get('digest'):
                    title = post.get('digest')
                elif post.get('noticeLinkTitle'):
                    title = post.get('noticeLinkTitle')

                # 方式2：从嵌套的post对象获取
                elif post.get('post'):
                    nested_post = post.get('post')
                    if nested_post.get('title'):
                        title = nested_post.get('title')
                    elif nested_post.get('digest'):
                        title = nested_post.get('digest')

                # 清理HTML标签
                if title:
                    title = self.clean_html(title).strip()

                # 如果仍然没有有效标题，使用默认标题
                if not title or title.isspace():
                    title = f'文章{i}'

                valid_posts.append((i, post, title))
                # 只显示前20篇文章，避免输出过多
                if len(valid_posts) <= 20:
                    print(f"{len(valid_posts):3d}. {title}")

            if len(valid_posts) > 20:
                print(f"... 还有 {len(valid_posts) - 20} 篇文章")

            if not valid_posts:
                print("❌ 没有找到有效的文章")
                return None

            print(f"\n找到 {len(valid_posts)} 篇有效文章")
            print("🎯 目标文章关键词:")
            print("   - 小鲛人")
            print("   - 王爷")
            print("   - 男风馆")
            print("   - 残缺的尾巴")
            print("   - 讨好所有人")

            # 自动搜索匹配的文章
            keywords = ['小鲛人', '王爷', '男风馆', '残缺', '尾巴', '讨好']

            print(f"\n🔍 自动搜索包含关键词的文章...")
            best_match = None
            best_score = 0

            # 显示所有匹配的文章
            matches = []
            for _, post, title in valid_posts:
                # 检查标题是否包含关键词
                matched_keywords = [keyword for keyword in keywords if keyword in title]
                match_count = len(matched_keywords)

                if match_count > 0:
                    matches.append((post, title, match_count, matched_keywords))
                    print(f"🎯 找到匹配: {title}")
                    print(f"   匹配关键词: {matched_keywords} (共{match_count}个)")

                if match_count > best_score:
                    best_score = match_count
                    best_match = (post, title)

                # 如果找到包含多个关键词的文章，直接返回
                if match_count >= 2:
                    print(f"✅ 自动选择最佳匹配: {title}")
                    return post

            if not matches:
                print("❌ 没有找到包含目标关键词的文章")
                print("💡 可能的原因:")
                print("   - 目标文章不在当前获取的文章列表中")
                print("   - 文章标题与预期关键词不匹配")
                print("   - 需要获取更多文章（尝试完整获取）")

            # 如果有部分匹配，询问用户
            if best_match and best_score > 0:
                post, title = best_match
                print(f"🤔 找到可能匹配的文章: {title}")
                print(f"   匹配关键词数: {best_score}")
                confirm = input("是否选择这篇文章? (y/n): ").strip().lower()
                if confirm == 'y':
                    return post

            # 让用户手动选择
            print("❌ 自动搜索未找到明确匹配的文章")
            print("💡 请手动选择文章:")

            try:
                choice = input(f"请输入文章序号 (1-{len(valid_posts)}): ").strip()
                if choice.isdigit():
                    choice_idx = int(choice) - 1
                    if 0 <= choice_idx < len(valid_posts):
                        _, post, title = valid_posts[choice_idx]
                        print(f"✅ 选择文章: {title}")
                        return post
                    else:
                        print("❌ 序号超出范围")
                        return None
                else:
                    print("❌ 请输入有效数字")
                    return None
            except KeyboardInterrupt:
                print("\n❌ 用户取消操作")
                return None

        except Exception as e:
            print(f"❌ 标题匹配失败: {e}")
            return None

    def _extract_article_content(self, post_data: Dict, article_url: str) -> str:
        """提取文章内容"""
        content = post_data.get('content', '')
        title = post_data.get('title', post_data.get('digest', '无标题'))
        author = post_data.get('blogInfo', {}).get('blogNickName', '未知作者')
        publish_time = post_data.get('publishTime', '')

        if publish_time:
            try:
                import datetime
                publish_time = datetime.datetime.fromtimestamp(int(publish_time)/1000).strftime('%Y-%m-%d %H:%M:%S')
            except:
                pass

        # 清理HTML标签
        content = self.clean_html(content)

        if not content.strip():
            print("⚠️ 文章内容为空，可能需要特殊权限访问")
            return None

        result = f"标题: {title}\n作者: {author}\n"
        if publish_time:
            result += f"发布时间: {publish_time}\n"
        result += f"原文链接: {article_url}\n"
        result += f"\n{'-'*50}\n\n{content}"

        print(f"✅ 成功获取文章内容 ({len(content)} 字符)")
        return result

    def clean_html(self, text: str) -> str:
        """清理HTML标签和转义字符"""
        if not text:
            return ""
        
        # 解码HTML实体
        text = html.unescape(text)
        
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        
        # 清理多余的空白字符
        text = re.sub(r'\n\s*\n', '\n\n', text)
        text = text.strip()
        
        return text
    
    def save_article(self, title: str, content: str, author: str, output_dir: str = "downloads"):
        """保存文章到文件"""
        try:
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 清理文件名中的非法字符
            safe_title = re.sub(r'[<>:"/\\|?*]', '_', title)
            safe_author = re.sub(r'[<>:"/\\|?*]', '_', author)
            
            filename = f"{safe_author}_{safe_title}.txt"
            filepath = os.path.join(output_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"作者: {author}\n")
                f.write(f"标题: {title}\n")
                f.write("=" * 50 + "\n\n")
                f.write(content)
            
            print(f"✅ 文章已保存: {filepath}")
            
        except Exception as e:
            print(f"❌ 保存文章失败: {e}")


def main():
    """主程序"""
    downloader = LofterDownloader()

    print("=" * 60)
    print("🌟 Lofter文章下载器")
    print("📚 基于JSON书源配置的智能下载工具")
    print("=" * 60)

    # 加载cookies
    if not downloader.load_cookies():
        print("\n🔐 Cookie设置向导")
        print("=" * 40)
        print("请按以下步骤设置cookies:")
        print("1. 在浏览器中访问并登录: https://newsmiss.lofter.com")
        print("2. 按F12打开开发者工具")
        print("3. 切换到Network（网络）标签页")
        print("4. 刷新页面")
        print("5. 找到任意请求，查看Request Headers")
        print("6. 复制Cookie头的完整值")
        print("7. 将Cookie值保存到cookies.txt文件中")
        print("\n💡 提示: Cookie应包含类似 'LOFTER-XXXXXX-LOGIN-AUTH' 的认证信息")

        while True:
            print("\n请选择:")
            print("1. 直接输入Cookie值")
            print("2. 我已创建cookies.txt文件，重新加载")
            print("3. 退出程序")

            choice = input("请选择 (1-3): ").strip()

            if choice == '1':
                cookie_input = input("\n请粘贴Cookie值: ").strip()
                if cookie_input:
                    downloader.save_cookies(cookie_input)
                    if downloader.load_cookies():
                        break
                    else:
                        print("❌ Cookie设置失败，请检查格式是否正确")
                else:
                    print("❌ Cookie值不能为空")
            elif choice == '2':
                if downloader.load_cookies():
                    break
                else:
                    print("❌ 仍然无法加载Cookie，请检查cookies.txt文件")
            elif choice == '3':
                print("👋 程序退出")
                return
            else:
                print("❌ 无效选择，请重新输入")
    
    while True:
        print("\n" + "=" * 50)
        print("📋 主菜单")
        print("=" * 50)
        print("1. 🔍 搜索作者")
        print("2. � 通过链接下载文章")
        print("3. �📖 测试功能")
        print("4. ❓ 帮助信息")
        print("5. 🚪 退出程序")

        choice = input("\n请选择操作 (1-5): ").strip()

        if choice == '1':
            # 搜索作者功能
            print("\n" + "🔍 作者搜索")
            print("=" * 30)
            print("💡 提示: 可以搜索作者的昵称或用户名")
            print("📝 示例: 村口做饭大厨")

            author_name = input("\n请输入作者名: ").strip()
            if not author_name:
                print("❌ 作者名不能为空")
                continue

            # 搜索作者
            search_result = downloader.search_author(author_name)
            if not search_result:
                print("\n💡 搜索建议:")
                print("- 检查作者名是否正确")
                print("- 尝试使用作者的完整昵称")
                print("- 确保网络连接正常")
                continue

            # 显示搜索结果
            blogs = search_result.get('blogs', [])
            if not blogs:
                print("❌ 未找到匹配的作者")
                continue

            print(f"\n✅ 找到 {len(blogs)} 个匹配的作者:")
            print("-" * 40)
            for i, blog in enumerate(blogs, 1):
                nick_name = blog.get('blogNickName', '未知')
                blog_name = blog.get('blogName', '未知')
                intro = blog.get('selfIntro', '无简介')[:30]
                print(f"{i}. {nick_name}")
                print(f"   用户名: @{blog_name}")
                print(f"   简介: {intro}...")
                print()

            try:
                blog_choice = input("请选择作者 (输入序号): ").strip()
                if not blog_choice.isdigit():
                    print("❌ 请输入有效的数字")
                    continue

                blog_index = int(blog_choice) - 1
                if 0 <= blog_index < len(blogs):
                    selected_blog = blogs[blog_index]

                    print(f"\n📚 正在获取 {selected_blog.get('blogNickName')} 的文章列表...")
                    print("请选择获取方式:")
                    print("1. 快速获取（最多500篇文章）")
                    print("2. 完整获取（所有文章，可能需要较长时间）")

                    get_choice = input("请选择 (1-2): ").strip()

                    if get_choice == '2':
                        # 获取全部文章
                        posts = downloader.get_all_author_posts(selected_blog)
                    else:
                        # 获取文章列表（默认最多500篇）
                        posts = downloader.get_author_posts(selected_blog)
                    if not posts:
                        print("💡 可能的原因:")
                        print("- 该作者没有公开文章")
                        print("- 需要特殊权限访问")
                        print("- 网络连接问题")
                        continue

                    # 显示文章列表
                    print(f"\n� {selected_blog.get('blogNickName')} 的文章列表 (共{len(posts)}篇):")
                    print("=" * 60)



                    for i, post in enumerate(posts, 1):
                        # 尝试多种方式获取标题
                        title = (post.get('title') or
                                post.get('digest') or
                                post.get('noticeLinkTitle') or
                                post.get('post', {}).get('title') or
                                post.get('post', {}).get('digest') or
                                f'文章{i}')

                        # 清理HTML标签
                        if title:
                            title = downloader.clean_html(title).strip()

                        # 如果标题为空或只有空白字符，使用默认标题
                        if not title or title.isspace():
                            title = f'文章{i}'

                        # 显示完整标题，不截断
                        print(f"{i:3d}. {title}")

                    print(f"\n{len(posts) + 1:3d}. 📦 下载全部文章")
                    print(f"{len(posts) + 2:3d}. 🔙 返回主菜单")

                    while True:
                        post_choice = input(f"\n请选择要下载的文章 (1-{len(posts)+2}): ").strip()

                        if post_choice == str(len(posts) + 2):
                            # 返回主菜单
                            break
                        elif post_choice == str(len(posts) + 1) or post_choice.lower() == 'all':
                            # 下载全部文章
                            print(f"\n📦 开始批量下载 {len(posts)} 篇文章...")
                            print("⏳ 请耐心等待，避免请求过于频繁...")

                            success_count = 0
                            for i, post in enumerate(posts, 1):
                                title = post.get('title', post.get('digest', f'文章{i}'))
                                print(f"\n[{i}/{len(posts)}] 下载: {title[:30]}...")

                                content = downloader.get_post_content(post)
                                if content:
                                    downloader.save_article(title, content, selected_blog.get('blogNickName', '未知作者'))
                                    success_count += 1
                                    print(f"✅ 成功")
                                else:
                                    print(f"❌ 失败")

                                # 避免请求过快
                                if i < len(posts):
                                    time.sleep(2)

                            print(f"\n🎉 批量下载完成!")
                            print(f"📊 成功: {success_count}/{len(posts)} 篇文章")
                            break
                        else:
                            try:
                                post_index = int(post_choice) - 1
                                if 0 <= post_index < len(posts):
                                    selected_post = posts[post_index]
                                    title = selected_post.get('title', selected_post.get('digest', '无标题'))

                                    print(f"\n📖 下载文章: {title}")
                                    print("⏳ 正在获取文章内容...")

                                    content = downloader.get_post_content(selected_post)
                                    if content:
                                        downloader.save_article(title, content, selected_blog.get('blogNickName', '未知作者'))
                                        print("✅ 文章下载完成!")

                                        # 询问是否继续下载
                                        continue_choice = input("\n是否继续下载其他文章? (y/n): ").strip().lower()
                                        if continue_choice != 'y':
                                            break
                                    else:
                                        print("❌ 文章下载失败")
                                else:
                                    print("❌ 无效的文章序号")
                            except ValueError:
                                print("❌ 请输入有效的数字")
                else:
                    print("❌ 无效的作者序号")
            except ValueError:
                print("❌ 请输入有效的数字")

        elif choice == '2':
            # 通过链接下载文章
            print("\n🔗 通过链接下载文章")
            print("=" * 40)
            print("💡 支持的链接格式:")
            print("   https://用户名.lofter.com/post/博客ID_文章ID")
            print("📝 示例:")
            print("   https://dmscdj.lofter.com/post/84bc89fd_2be5a7548?incantation=rzD5Hc6Y0MGF")

            article_url = input("\n请输入文章链接: ").strip()
            if not article_url:
                print("❌ 文章链接不能为空")
                continue

            if not article_url.startswith('https://') or '.lofter.com/post/' not in article_url:
                print("❌ 链接格式不正确")
                print("💡 请确保链接格式为: https://用户名.lofter.com/post/...")
                continue

            print(f"\n🔗 开始下载文章...")
            content = downloader.download_from_url(article_url)

            if content:
                # 从内容中提取标题和作者
                lines = content.split('\n')
                title = "未知标题"
                author = "未知作者"

                for line in lines:
                    if line.startswith('标题: '):
                        title = line[3:].strip()
                    elif line.startswith('作者: '):
                        author = line[3:].strip()

                # 保存文章
                downloader.save_article(title, content, author)
                print("✅ 文章下载并保存成功!")
            else:
                print("❌ 文章下载失败")
                print("💡 可能的原因:")
                print("- 链接格式不正确")
                print("- 文章需要特殊权限访问")
                print("- Cookie认证失败")
                print("- 文章不存在或已被删除")

        elif choice == '3':
            # 通过链接下载文章
            print("\n🔗 通过链接下载文章")
            print("=" * 40)
            print("💡 支持的链接格式:")
            print("   https://用户名.lofter.com/post/博客ID_文章ID")
            print("📝 示例:")
            print("   https://dmscdj.lofter.com/post/84bc89fd_2be5a7548?incantation=rzD5Hc6Y0MGF")

            article_url = input("\n请输入文章链接: ").strip()
            if not article_url:
                print("❌ 文章链接不能为空")
                continue

            if not article_url.startswith('https://') or '.lofter.com/post/' not in article_url:
                print("❌ 链接格式不正确")
                print("💡 请确保链接格式为: https://用户名.lofter.com/post/...")
                continue

            print(f"\n🔗 开始下载文章...")
            content = downloader.download_from_url(article_url)

            if content:
                # 从内容中提取标题和作者
                lines = content.split('\n')
                title = "未知标题"
                author = "未知作者"

                for line in lines:
                    if line.startswith('标题: '):
                        title = line[3:].strip()
                    elif line.startswith('作者: '):
                        author = line[3:].strip()

                # 保存文章
                downloader.save_article(title, content, author)
                print("✅ 文章下载并保存成功!")
            else:
                print("❌ 文章下载失败")
                print("💡 可能的原因:")
                print("- 链接格式不正确")
                print("- 文章需要特殊权限访问")
                print("- Cookie认证失败")
                print("- 文章不存在或已被删除")

        elif choice == '3':
            # 测试功能
            print("\n🧪 功能测试")
            print("=" * 30)
            print("这将运行测试脚本来验证各个功能模块")

            test_choice = input("是否运行测试? (y/n): ").strip().lower()
            if test_choice == 'y':
                try:
                    import test_downloader
                    test_downloader.main()
                except ImportError:
                    print("❌ 测试脚本不存在")
                except Exception as e:
                    print(f"❌ 测试运行失败: {e}")

        elif choice == '4':
            # 帮助信息
            print("\n❓ 帮助信息")
            print("=" * 40)
            print("📖 使用说明:")
            print("1. 确保已正确设置Cookie认证")
            print("2. 使用'搜索作者'功能查找目标作者")
            print("3. 从搜索结果中选择正确的作者")
            print("4. 浏览该作者的文章列表")
            print("5. 选择单篇文章或批量下载")
            print("6. 或者直接使用'通过链接下载文章'功能")
            print()
            print("� 链接下载说明:")
            print("- 支持直接输入文章链接进行下载")
            print("- 链接格式: https://用户名.lofter.com/post/...")
            print("- 示例: https://dmscdj.lofter.com/post/84bc89fd_2be5a7548")
            print()
            print("�🔧 故障排除:")
            print("- 认证失败: 重新获取Cookie")
            print("- 搜索无结果: 检查作者名拼写")
            print("- 下载失败: 检查网络连接")
            print("- 链接无效: 检查链接格式是否正确")
            print()
            print("📁 文件说明:")
            print("- cookies.txt: 存储登录认证信息")
            print("- downloads/: 下载的文章保存目录")
            print("- test_downloader.py: 功能测试脚本")

        elif choice == '5':
            print("\n👋 感谢使用Lofter文章下载器!")
            print("🌟 如有问题，请检查帮助信息或重新设置Cookie")
            break
        else:
            print("❌ 无效的选择，请输入1-5之间的数字")


if __name__ == "__main__":
    main()
