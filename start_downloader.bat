@echo off
chcp 65001 >nul
title Lofter文章下载器

echo ========================================
echo    Lofter文章下载器 启动脚本
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python已安装

REM 检查依赖是否安装
python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo 📦 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
) else (
    echo ✅ 依赖包已安装
)

echo.
echo 🚀 启动下载器...
echo.

REM 启动主程序
python lofter_downloader.py

echo.
echo 程序已退出，按任意键关闭窗口...
pause >nul
