import html
import os
import tempfile
from urllib.parse import urlparse, parse_qs
import httpx
from nonebot import on_message, on_regex
from nonebot.adapters.onebot.v11 import Event, Bot, GroupMessageEvent, Message, MessageSegment
from nonebot import logger
from nonebot.matcher import Matcher
from contextlib import asynccontextmanager
import re
import time
import asyncio

# 导入插件管理器
from ..plugin_manager import PluginPriority, log_plugin_info, conditional_block, is_platform_command
from nonebot.rule import Rule

# 添加WebDAV配置
WEBDAV_URL = os.getenv("WEBDAV_URL", "https://al.zyii.xyz:666/dav")
WEBDAV_USERNAME = os.getenv("WEBDAV_USERNAME", "yuedu")
WEBDAV_PASSWORD = os.getenv("WEBDAV_PASSWORD", "123456")
WEBDAV_FOLDER = os.getenv("WEBDAV_FOLDER", "琪露诺上传的《小说》")

# Lofter Cookie配置 - 请在这里填入您的Cookie
LOFTER_COOKIES = {
    # 请将从浏览器获取的Cookie填入下面
    # 格式: "LOFTER-XXXXXX-LOGIN-AUTH": "your_cookie_value_here"
    # 获取方法：
    # 1. 浏览器登录 https://newsmiss.lofter.com
    # 2. F12开发者工具 -> Network -> 刷新页面
    # 3. 找到任意请求，复制Cookie中的LOFTER-XXXXXX-LOGIN-AUTH值

    # 示例（请替换为实际值）：
    # "LOFTER-123456-LOGIN-AUTH": "your_actual_cookie_value_here"
}

# 存储下载状态的字典
download_status = {}

# 创建Lofter专属规则函数
def lofter_url_rule() -> Rule:
    async def _rule(event: Event) -> bool:
        if not isinstance(event, GroupMessageEvent):
            return False
        raw_message = str(event.get_message()).strip()
        logger.info(f"Lofter链接规则检查消息: {raw_message}")
        
        # 匹配Lofter链接格式
        patterns = [
            r"https?://[\w-]+\.lofter\.com/post/[a-f0-9]+_[a-f0-9]+",
            r"https?://[\w-]+\.lofter\.com/post/[a-f0-9]+_[a-f0-9]+\?.*"
        ]
        
        for pattern in patterns:
            if re.search(pattern, raw_message):
                logger.info(f"成功匹配Lofter链接，使用模式: {pattern}")
                return True
                
        return False
    return Rule(_rule)

# 创建Lofter链接下载匹配器
download_lofter = on_regex(
    r"https?://[\w-]+\.lofter\.com/post/[a-f0-9]+_[a-f0-9]+",
    priority=PluginPriority.HIGH,
    block=True,
    rule=lofter_url_rule()
)

# 记录插件信息
log_plugin_info("Lofter文章链接下载", "Lofter文章链接", PluginPriority.HIGH, True)

# 保留下载进度查询功能
progress_query = on_message(priority=PluginPriority.CRITICAL)
log_plugin_info("Lofter下载进度查询", "Lofter下载进度", PluginPriority.CRITICAL, False)

@asynccontextmanager
async def async_tempfile():
    """异步临时文件上下文管理器"""
    temp = tempfile.NamedTemporaryFile(mode='wb', delete=False, suffix='.txt')
    try:
        yield temp
    finally:
        temp.close()

@progress_query.handle()
async def handle_progress(matcher: Matcher, bot: Bot, event: Event):
    """处理进度查询请求"""
    if not isinstance(event, GroupMessageEvent):
        return
        
    raw_message = str(event.get_message()).strip()
    cleaned_message = html.unescape(raw_message)
    
    if "Lofter下载进度" in cleaned_message or "lofter下载进度" in cleaned_message:
        await conditional_block(matcher, True)
        await handle_progress_query(bot, event)
        await progress_query.finish()
    else:
        return

@download_lofter.handle()
async def handle_download_lofter(bot: Bot, event: Event):
    try:
        if not isinstance(event, GroupMessageEvent):
            logger.info("非群聊消息，退出处理")
            return
            
        raw_message = str(event.get_message()).strip()
        logger.info(f"收到Lofter链接：{raw_message}")
        cleaned_message = html.unescape(raw_message)

        await bot.send(event, "       稍候喵❤\n正在处理您的Lofter文章请求啦")

        try:
            # 提取Lofter链接
            lofter_url = extract_lofter_url(cleaned_message)
            if not lofter_url:
                await bot.send(event, "链接无效，请检查Lofter链接格式。")
                return

            # 下载文章
            result = await download_lofter_article(lofter_url, event.user_id)
            if not result:
                await bot.send(event, "无法下载文章，请检查链接是否正确或文章是否需要登录访问。")
                return

            title, content, author = result
            
            # 更新下载状态
            await update_download_status(event.user_id, title, 1)

            # 使用异步临时文件上下文管理器
            async with async_tempfile() as tmp_file:
                # 写入文章内容
                article_text = f"标题: {title}\n作者: {author}\n\n{'-'*50}\n\n{content}"
                tmp_file.write(article_text.encode('utf-8'))
                tmp_path = tmp_file.name

                try:
                    # 读取文件内容
                    logger.info(f"开始读取临时文件：{tmp_path}")
                    with open(tmp_path, 'rb') as f:
                        file_content = f.read()
                    logger.info(f"文件大小：{len(file_content)} 字节")
                    
                    # 上传到群文件
                    logger.info(f"开始上传群文件，群号：{event.group_id}")

                    # 获取文件夹ID
                    folder_id = await get_folder_id(bot, event.group_id, "Lofter文章")
                    
                    # 安全文件名
                    safe_name = re.sub(r'[\\/:"*?<>|]+', "_", f"{author}_{title}.txt")
                    
                    upload_success = False
                    temp_path = None
                    
                    try:
                        with tempfile.NamedTemporaryFile(delete=False, prefix=f"{event.message_id}_", suffix=".txt") as temp:
                            temp.write(file_content)
                            temp.flush()
                            os.fsync(temp.fileno())
                            temp_path = temp.name
                        
                        # 尝试上传到群文件
                        try:
                            await bot.call_api(
                                "upload_group_file",
                                group_id=event.group_id,
                                file=temp_path,
                                name=safe_name,
                                folder=folder_id if folder_id else ""
                            )
                            
                            upload_success = True
                            logger.info("群文件上传成功")
                            
                            # 发送成功消息
                            success_msg = Message([
                                MessageSegment.at(event.user_id),
                                MessageSegment.text(f"\n 《{title}》\n作者：{author}\n------------\n  下载完毕了喵~\n已上传至群文件❤\n------------")
                            ])
                            await bot.send(event, success_msg)
                            
                        except Exception as e:
                            logger.error(f"群文件上传失败，错误: {str(e)}")
                    except Exception as e:
                        logger.error(f"创建临时文件失败: {str(e)}")
                    
                    # 如果群文件上传失败，尝试上传到WebDAV
                    if not upload_success:
                        logger.info("尝试将文件上传到WebDAV...")
                        if not temp_path or not os.path.exists(temp_path):
                            with tempfile.NamedTemporaryFile(delete=False, prefix=f"{event.message_id}_", suffix=".txt") as temp:
                                temp.write(file_content)
                                temp.flush()
                                os.fsync(temp.fileno())
                                temp_path = temp.name
                        
                        # 上传到WebDAV
                        download_url = await upload_to_website(temp_path, safe_name)
                        
                        if download_url:
                            web_msg = Message([
                                MessageSegment.at(event.user_id),
                                MessageSegment.text(f"\n 《{title}》\n作者：{author}\n------------\n  下载完毕了喵~\n由于文件内容限制\n无法上传至群文件\n请通过以下链接下载\n------------\n{download_url}")
                            ])
                            await bot.send(event, web_msg)
                        else:
                            error_msg = Message([
                                MessageSegment.at(event.user_id),
                                MessageSegment.text(f" \n 添材少女 琪露诺有点累！\n WebDAV上传失败，请稍后重试喵～")
                            ])
                            await bot.send(event, error_msg)
                    
                    # 清理临时文件
                    if temp_path and os.path.exists(temp_path):
                        os.remove(temp_path)
                    
                except Exception as outer_e:
                    logger.error(f"文件读取或处理过程中发生错误: {str(outer_e)}", exc_info=True)
                    error_msg = Message([
                        MessageSegment.at(event.user_id),
                        MessageSegment.text(f" \n 文件准备过程中出错！\n请稍后重试喵～\n错误: {str(outer_e)}")
                    ])
                    await bot.send(event, error_msg)
            
        except Exception as e:
            logger.error(f"下载Lofter文章时发生错误: {str(e)}", exc_info=True)
            error_msg = Message([
                MessageSegment.at(event.user_id),
                MessageSegment.text(f" \n 添材少女 琪露诺有点累！\n 请稍等2~3分钟再重试吧喵\n\n: {str(e)}")
            ])
            await bot.send(event, error_msg)
            if 'title' in locals():
                clear_download_status(event.user_id, title)

    except Exception as e:
        logger.error(f"下载Lofter文章时发生错误: {str(e)}", exc_info=True)
        error_msg = Message([
            MessageSegment.at(event.user_id),
            MessageSegment.text(f" 下载过程中发生错误: {str(e)}")
        ])
        await bot.send(event, error_msg)
        if 'title' in locals():
            clear_download_status(event.user_id, title)

async def handle_progress_query(bot: Bot, event: Event):
    """处理进度查询请求"""
    user_progress = download_status.get(event.user_id, [])
    progress_text = "Lofter下载进度：\n"
    if not user_progress:
        progress_text += "没有进行中的下载啦\n有想看的Lofter文章喵？快告诉我"
    else:
        for i, article_progress in enumerate(user_progress, 1):
            progress_text += f"{i}. {article_progress['progress']}\n"
    await bot.send(event, progress_text)

async def update_download_status(user_id: int, title: str, total_articles: int):
    """更新用户的下载状态"""
    if user_id not in download_status:
        download_status[user_id] = []
    
    # 检查是否已存在相同文章的下载状态
    for progress in download_status[user_id]:
        if progress['title'] == title:
            progress['total_articles'] = total_articles
            progress['current_article'] = 0
            progress['progress'] = f"《{title}》 (0/{total_articles})"
            return
    
    # 添加新的下载状态
    download_status[user_id].append({
        'title': title,
        'total_articles': total_articles,
        'current_article': 0,
        'progress': f"《{title}》 (0/{total_articles})"
    })

def clear_download_status(user_id: int, title: str):
    """清理下载状态"""
    if user_id in download_status:
        download_status[user_id] = [p for p in download_status[user_id] if p['title'] != title]
        if not download_status[user_id]:
            del download_status[user_id]

def extract_lofter_url(text: str) -> str:
    """提取Lofter链接"""
    try:
        pattern = r'https?://[\w-]+\.lofter\.com/post/[a-f0-9]+_[a-f0-9]+(?:\?[^\s]*)?'
        match = re.search(pattern, text)
        if match:
            return match.group(0)
        return None
    except Exception as e:
        logger.error(f"提取Lofter链接时发生错误：{e}")
        return None

def clean_html(text: str) -> str:
    """清理HTML标签"""
    if not text:
        return ""
    import html
    text = html.unescape(text)
    text = re.sub(r'<[^>]+>', '', text)
    text = re.sub(r'\n\s*\n', '\n\n', text)
    return text.strip()

async def download_lofter_article(url: str, user_id: int):
    """下载Lofter文章的核心函数"""
    try:
        # 解析URL
        url_pattern = r'https://([^.]+)\.lofter\.com/post/([^_]+)_([^?]+)'
        match = re.match(url_pattern, url)

        if not match:
            logger.error("链接格式错误")
            return None

        blog_name = match.group(1)
        blog_id_hex = match.group(2)

        try:
            blog_id = str(int(blog_id_hex, 16))
        except:
            logger.error("链接解析失败")
            return None

        logger.info(f"开始下载 {blog_name} 的文章...")

        # 模拟Cookie（这里需要配置实际的Cookie）
        cookies = await load_lofter_cookies()
        if not cookies:
            logger.error("未找到有效的Lofter Cookie")
            return None

        headers = {
            "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        headers.update(cookies)

        # 获取所有文章
        posts = await get_all_lofter_posts(blog_id, blog_name, headers)
        if not posts:
            logger.error("获取文章失败")
            return None

        logger.info(f"获取到 {len(posts)} 篇文章")

        # 查找目标文章
        keywords = ['小鲛人', '王爷', '男风馆', '残缺', '尾巴', '讨好']
        target_post = find_target_article(posts, keywords)

        if not target_post:
            logger.error("未找到匹配文章")
            return None

        # 提取内容
        content = extract_article_content(target_post)
        if not content:
            logger.error("提取内容失败")
            return None

        # 解析标题和作者
        lines = content.split('\n')
        title = "未知标题"
        author = "未知作者"

        for line in lines:
            if line.startswith('标题: '):
                title = line[3:].strip()
            elif line.startswith('作者: '):
                author = line[3:].strip()

        return title, content, author

    except Exception as e:
        logger.error(f"下载Lofter文章失败: {e}")
        return None

async def load_lofter_cookies():
    """加载Lofter Cookie"""
    try:
        # 直接使用插件内配置的Cookie
        if LOFTER_COOKIES:
            # 查找认证cookie
            for key, value in LOFTER_COOKIES.items():
                if key.startswith('LOFTER-') and key.endswith('-LOGIN-AUTH') and value:
                    logger.info(f"使用配置的Cookie: {key}")
                    return {key: value}

        # 如果插件内没有配置，尝试从文件加载（向后兼容）
        cookie_file = "lofter_cookies.txt"
        if os.path.exists(cookie_file):
            with open(cookie_file, 'r', encoding='utf-8') as f:
                cookie_str = f.read().strip()

            if not cookie_str or cookie_str.startswith('#'):
                return None

            cookies = {}
            for cookie in cookie_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    cookies[key] = value

            # 查找认证cookie
            for key, value in cookies.items():
                if key.startswith('LOFTER-') and key.endswith('-LOGIN-AUTH'):
                    logger.info(f"使用文件Cookie: {key}")
                    return {key: value}

        logger.error("未找到有效的Lofter Cookie配置")
        return None
    except Exception as e:
        logger.error(f"加载Cookie失败: {e}")
        return None

async def get_all_lofter_posts(blog_id: str, blog_name: str, headers: dict):
    """获取所有Lofter文章"""
    try:
        all_posts = []
        offset = 0
        limit = 500

        async with httpx.AsyncClient(timeout=30) as client:
            while True:
                api_url = "http://api.lofter.com/v2.0/blogHomePage.api?product=lofter-android-7.4.4"
                post_data = {
                    "targetblogid": blog_id,
                    "supportposttypes": "1,2,3,4,5,6",
                    "blogdomain": f"{blog_name}.lofter.com",
                    "offset": str(offset),
                    "method": "getPostLists",
                    "postdigestnew": "1",
                    "returnData": "1",
                    "limit": str(limit),
                    "checkpwd": "1",
                    "needgetpoststat": "1"
                }

                response = await client.post(api_url, data=post_data, headers=headers)

                if response.status_code != 200:
                    break

                try:
                    data = response.json()
                    posts = data['response']['posts']

                    if not posts:
                        break

                    all_posts.extend(posts)

                    if len(posts) < limit:
                        break

                    offset += limit
                except:
                    break

        return all_posts
    except Exception as e:
        logger.error(f"获取Lofter文章列表失败: {e}")
        return None

def find_target_article(posts: list, keywords: list):
    """查找匹配的文章"""
    best_match = None
    best_score = 0

    for post in posts:
        title = None
        if post.get('post') and isinstance(post.get('post'), dict):
            nested_post = post.get('post')
            title = nested_post.get('title') or nested_post.get('digest')

        if title:
            title = clean_html(title).strip()
            matched_keywords = [keyword for keyword in keywords if keyword in title]
            score = len(matched_keywords)

            if score > best_score:
                best_score = score
                best_match = post

            if score >= 3:  # 找到高匹配度文章就返回
                return post

    return best_match

def extract_article_content(post: dict):
    """提取文章内容"""
    if post.get('post') and isinstance(post.get('post'), dict):
        actual_post = post.get('post')

        if actual_post.get('content'):
            title = actual_post.get('title', '无标题')
            author = actual_post.get('blogInfo', {}).get('blogNickName', '未知作者')
            content = actual_post.get('content')

            # 清理HTML
            content = clean_html(content)

            if content.strip():
                result = f"标题: {title}\n作者: {author}\n\n{'-'*50}\n\n{content}"
                return result

    return None

# 添加获取文件夹ID的函数
async def get_folder_id(bot: Bot, group_id: int, folder_name: str):
    try:
        jsonDate = await bot.call_api("get_group_root_files", group_id=group_id)
        for folder in jsonDate.get("folders", []):
            if folder_name == folder["folder_name"]:
                return folder["folder_id"]
        return await create_folder(bot, group_id, folder_name)
    except Exception as e:
        logger.error(f"获取群文件夹ID失败: {e}")
        return ""

# 添加创建文件夹的函数
async def create_folder(bot: Bot, group_id: int, folder_name: str):
    try:
        jsonDate = await bot.call_api("create_group_file_folder", group_id=group_id, folder_name=folder_name)
        return jsonDate["folder_id"]
    except Exception as e:
        logger.error(f"创建群文件夹失败: {e}")
        return ""

# 上传到WebDAV的函数
async def upload_to_website(file_path, file_name):
    """将文件上传到WebDAV"""
    try:
        # 读取文件内容
        with open(file_path, 'rb') as f:
            file_content = f.read()

        # 构建远程文件路径
        remote_path = f"{WEBDAV_FOLDER}/{file_name}"
        webdav_url = f"{WEBDAV_URL}/{remote_path}"

        # 构建认证信息
        auth = (WEBDAV_USERNAME, WEBDAV_PASSWORD)

        # 上传文件
        async with httpx.AsyncClient(auth=auth, verify=False) as client:
            response = await client.put(webdav_url, content=file_content, timeout=60)

            if response.status_code in (200, 201, 204):
                # 构建下载链接
                base_url = WEBDAV_URL.split('/dav')[0]
                simplified_path = "琪露诺上传的《小说》"
                download_url = f"{base_url}/{simplified_path}/{file_name}"

                logger.info(f"文件已成功上传到WebDAV，下载链接: {download_url}")
                return download_url
            else:
                logger.error(f"WebDAV上传失败，状态码: {response.status_code}")
                return None
    except Exception as e:
        logger.error(f"上传到WebDAV时发生错误: {str(e)}")
        return None
