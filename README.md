# Lofter文章下载器

基于提供的JSON书源配置实现的控制台下载工具，支持通过Cookie登录访问，按作者搜索和下载文章。

## 功能特点

- ✅ 支持Cookie登录认证
- ✅ 按作者名搜索文章（使用@作者名格式）
- ✅ 显示作者的所有文章列表
- ✅ 支持单篇文章下载
- ✅ 支持批量下载作者的所有文章
- ✅ **新增：直接通过文章链接下载**
- ✅ 支持分页获取作者的全部文章
- ✅ 自动清理HTML标签，保存为纯文本
- ✅ 友好的控制台交互界面

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 获取Cookie

首先需要获取Lofter的登录Cookie：

1. 在浏览器中访问 https://newsmiss.lofter.com 并登录
2. 按F12打开开发者工具
3. 切换到Network（网络）标签页
4. 刷新页面
5. 找到任意一个请求，查看Request Headers
6. 复制Cookie头的完整值

### 2. 设置Cookie

有两种方式设置Cookie：

**方式一：创建cookies.txt文件**
```
在项目目录下创建cookies.txt文件，将复制的Cookie值粘贴进去
```

**方式二：程序运行时输入**
```
运行程序时，如果没有找到cookies.txt文件，程序会提示你直接输入Cookie值
```

### 3. 运行程序

```bash
python lofter_downloader.py
```

### 4. 使用流程

1. 程序启动后会自动加载Cookie
2. 选择"搜索作者"
3. 输入作者名（例如：村口做饭大厨）
4. 从搜索结果中选择目标作者
5. 查看该作者的文章列表
6. 选择要下载的文章：
   - 输入文章序号下载单篇文章
   - 输入'all'或最后一个序号下载全部文章

## 示例

```
🌟 Lofter文章下载器
============================================================
✅ Cookies加载成功

========================================
请选择操作:
1. 搜索作者
2. 退出
请输入选择 (1-2): 1
请输入作者名 (例如: 村口做饭大厨): 村口做饭大厨

🔍 搜索作者: 村口做饭大厨
📡 请求URL: https://api.lofter.com/newsearch/blog.json?key=%E6%9D%91%E5%8F%A3%E5%81%9A%E9%A5%AD%E5%A4%A7%E5%8E%A8&limit=10&offset=0

找到 1 个匹配的作者:
1. 村口做饭大厨 (@xxxxx)
请选择作者 (输入序号): 1

📚 获取作者文章列表...
✅ 找到 25 篇文章

📚 村口做饭大厨 的文章列表:
1. ♂♂小鲛人被王爷亲手送进男风馆后，他学会了用残缺的尾巴讨好所有人
2. 其他文章标题...
...
26. 下载全部文章

请选择要下载的文章 (输入序号或'all'下载全部): 1
```

## 文件结构

```
lofter_downloader/
├── lofter_downloader.py    # 主程序
├── requirements.txt        # 依赖包列表
├── README.md              # 使用说明
├── cookies.txt            # Cookie文件（需要手动创建）
└── downloads/             # 下载的文章保存目录
    ├── 村口做饭大厨_文章1.txt
    ├── 村口做饭大厨_文章2.txt
    └── ...
```

## 注意事项

1. **Cookie有效期**：Cookie会过期，如果出现认证失败，需要重新获取Cookie
2. **请求频率**：程序在批量下载时会自动添加延时，避免请求过于频繁
3. **文件命名**：下载的文章会自动清理文件名中的非法字符
4. **网络问题**：如果遇到网络错误，可以重试操作

## 技术实现

本下载器严格按照提供的JSON书源配置实现：

- 使用JSON中定义的API端点和参数
- 遵循搜索规则（@作者名格式）
- 按照内容提取规则处理文章内容
- 实现Cookie认证机制

## 故障排除

**问题1：Cookie认证失败**
- 解决：重新获取Cookie并更新cookies.txt文件

**问题2：搜索不到作者**
- 解决：检查作者名是否正确，确保网络连接正常

**问题3：文章内容为空**
- 解决：可能是文章需要特殊权限或已被删除

**问题4：下载速度慢**
- 解决：这是正常现象，程序会自动控制请求频率以避免被封禁
