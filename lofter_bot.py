#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lofter文章下载
"""

import html
import os
import tempfile
import httpx
from nonebot import on_message, on_regex
from nonebot.adapters.onebot.v11 import Event, Bot, GroupMessageEvent, Message, MessageSegment
from nonebot import logger
from nonebot.matcher import Matcher
from contextlib import asynccontextmanager
import re
import time
import asyncio

# ==================== 配置区域 ====================
# 请在这里填入您的Lofter Cookie
LOFTER_COOKIE_KEY = "LOFTER-PHONE-LOGIN-AUTH"
LOFTER_COOKIE_VALUE = "JxNkxwh5V60_c2ha0_61GMAdR9FTtoGpOaOMvp3akrxrfTq1uLuDhIO47vTZEqmHSQ21n_2u70k1QUdfXgS9SFDB_zB32lo-"

# WebDAV配置（可选）
WEBDAV_URL = "https://al.zyii.xyz:666/dav"
WEBDAV_USERNAME = "yuedu"
WEBDAV_PASSWORD = "123456"
WEBDAV_FOLDER = "琪露诺上传的《小说》"

# 文章匹配设置
ENABLE_KEYWORD_MATCHING = False  # 是否启用关键词匹配作为备选方案
ARTICLE_KEYWORDS = []  # 关键词列表（仅在启用关键词匹配时使用）

# 说明：
# - 默认通过链接中的文章ID直接查找文章（适用于所有文章）
# - 如果ID查找失败且启用了关键词匹配，会尝试通过关键词查找
# - 如果都失败，会下载作者的第一篇文章
# ==================== 配置区域结束 ====================

# 导入插件管理器
try:
    from ..plugin_manager import PluginPriority, log_plugin_info, conditional_block
    from nonebot.rule import Rule
except ImportError:
    # 如果没有插件管理器，使用默认值
    class PluginPriority:
        HIGH = 5
        CRITICAL = 1
        ULTRA_HIGH = 0  # 最高优先级

    def log_plugin_info(name, desc, priority, block):
        logger.info(f"插件: {name} - {desc}")

    async def conditional_block(matcher, block):
        pass

    from nonebot.rule import Rule

# 存储下载状态
download_status = {}

# 创建Lofter链接匹配规则
def lofter_url_rule() -> Rule:
    async def _rule(event: Event) -> bool:
        if not isinstance(event, GroupMessageEvent):
            return False
        raw_message = str(event.get_message()).strip()
        logger.info(f"Lofter插件检查消息: {raw_message}")

        # 匹配Lofter链接
        pattern = r"https?://[\w-]+\.lofter\.com/post/[a-f0-9]+_[a-f0-9]+"
        is_match = bool(re.search(pattern, raw_message))

        if is_match:
            logger.info(f"✅ Lofter链接匹配成功: {raw_message}")
        else:
            logger.info(f"❌ 不是Lofter链接: {raw_message}")

        return is_match
    return Rule(_rule)

# 创建匹配器 - 使用最高优先级确保能拦截Lofter链接
download_lofter = on_regex(
    r"https?://[\w-]+\.lofter\.com/post/[a-f0-9]+_[a-f0-9]+",
    priority=1,  # 使用最高优先级
    block=True,
    rule=lofter_url_rule()
)

progress_query = on_message(priority=1)  # 同样使用最高优先级

# 记录插件信息
log_plugin_info("Lofter文章下载", "Lofter文章链接下载", PluginPriority.HIGH, True)
log_plugin_info("Lofter下载进度", "下载进度查询", PluginPriority.CRITICAL, False)

@asynccontextmanager
async def async_tempfile():
    """异步临时文件管理器"""
    temp = tempfile.NamedTemporaryFile(mode='wb', delete=False, suffix='.txt')
    try:
        yield temp
    finally:
        temp.close()

@progress_query.handle()
async def handle_progress(matcher: Matcher, bot: Bot, event: Event):
    """处理进度查询"""
    if not isinstance(event, GroupMessageEvent):
        return
        
    raw_message = str(event.get_message()).strip()
    cleaned_message = html.unescape(raw_message)
    
    if "Lofter下载进度" in cleaned_message or "lofter下载进度" in cleaned_message:
        await conditional_block(matcher, True)
        
        user_progress = download_status.get(event.user_id, [])
        progress_text = "Lofter下载进度：\n"
        if not user_progress:
            progress_text += "没有进行中的下载啦\n有想看的Lofter文章喵？快告诉我"
        else:
            for i, article_progress in enumerate(user_progress, 1):
                progress_text += f"{i}. {article_progress['progress']}\n"
        
        await bot.send(event, progress_text)
        await progress_query.finish()

@download_lofter.handle()
async def handle_download_lofter(bot: Bot, event: Event):
    """处理Lofter链接下载"""
    try:
        logger.info("🎯 Lofter下载处理器被触发！")

        if not isinstance(event, GroupMessageEvent):
            logger.info("❌ 不是群聊消息，退出处理")
            return

        raw_message = str(event.get_message()).strip()
        logger.info(f"🔗 开始处理Lofter链接：{raw_message}")
        
        # 检查Cookie配置
        if not LOFTER_COOKIE_KEY or not LOFTER_COOKIE_VALUE:
            await bot.send(event, "❌ 未配置Lofter Cookie，请联系管理员配置")
            return
        
        cleaned_message = html.unescape(raw_message)
        await bot.send(event, "       稍候喵❤\n正在处理您的Lofter文章请求啦")

        # 提取链接
        lofter_url = extract_lofter_url(cleaned_message)
        if not lofter_url:
            await bot.send(event, "❌ 链接格式错误，请检查Lofter链接")
            return

        # 下载文章
        result = await download_lofter_article(lofter_url)
        if not result:
            await bot.send(event, "❌ 下载失败，请检查链接或稍后重试")
            return

        title, content, author = result
        
        # 更新下载状态
        await update_download_status(event.user_id, title, 1)

        # 创建文件内容
        article_text = f"标题: {title}\n作者: {author}\n\n{'-'*50}\n\n{content}"
        
        # 使用临时文件
        async with async_tempfile() as tmp_file:
            tmp_file.write(article_text.encode('utf-8'))
            tmp_path = tmp_file.name

            try:
                # 读取文件
                with open(tmp_path, 'rb') as f:
                    file_content = f.read()
                
                # 安全文件名
                safe_name = re.sub(r'[\\/:"*?<>|]+', "_", f"{author}_{title}.txt")
                
                upload_success = False
                temp_path = None
                
                try:
                    # 创建临时文件用于上传
                    with tempfile.NamedTemporaryFile(delete=False, prefix=f"{event.message_id}_", suffix=".txt") as temp:
                        temp.write(file_content)
                        temp.flush()
                        os.fsync(temp.fileno())
                        temp_path = temp.name
                    
                    # 尝试上传到群文件
                    try:
                        folder_id = await get_folder_id(bot, event.group_id, "Lofter文章")
                        await bot.call_api(
                            "upload_group_file",
                            group_id=event.group_id,
                            file=temp_path,
                            name=safe_name,
                            folder=folder_id if folder_id else ""
                        )
                        
                        upload_success = True
                        logger.info("群文件上传成功")
                        
                        # 发送成功消息
                        success_msg = Message([
                            MessageSegment.at(event.user_id),
                            MessageSegment.text(f"\n 《{title}》\n作者：{author}\n------------\n  下载完毕了喵~\n已上传至群文件❤\n------------")
                        ])
                        await bot.send(event, success_msg)
                        
                    except Exception as e:
                        logger.error(f"群文件上传失败: {e}")
                
                except Exception as e:
                    logger.error(f"文件处理失败: {e}")
                
                # 如果群文件上传失败，尝试WebDAV
                if not upload_success:
                    logger.info("尝试上传到WebDAV...")
                    if not temp_path or not os.path.exists(temp_path):
                        with tempfile.NamedTemporaryFile(delete=False, prefix=f"{event.message_id}_", suffix=".txt") as temp:
                            temp.write(file_content)
                            temp.flush()
                            temp_path = temp.name
                    
                    download_url = await upload_to_webdav(temp_path, safe_name)
                    
                    if download_url:
                        web_msg = Message([
                            MessageSegment.at(event.user_id),
                            MessageSegment.text(f"\n 《{title}》\n作者：{author}\n------------\n  下载完毕了喵~\n请通过以下链接下载\n------------\n{download_url}")
                        ])
                        await bot.send(event, web_msg)
                    else:
                        error_msg = Message([
                            MessageSegment.at(event.user_id),
                            MessageSegment.text(f" \n 下载失败，请稍后重试喵～")
                        ])
                        await bot.send(event, error_msg)
                
                # 清理临时文件
                if temp_path and os.path.exists(temp_path):
                    os.remove(temp_path)
                
            except Exception as e:
                logger.error(f"文件处理错误: {e}")
                error_msg = Message([
                    MessageSegment.at(event.user_id),
                    MessageSegment.text(f" \n 文件处理失败，请稍后重试喵～")
                ])
                await bot.send(event, error_msg)
        
        # 清理下载状态
        clear_download_status(event.user_id, title)
        
    except Exception as e:
        logger.error(f"下载Lofter文章错误: {e}")
        error_msg = Message([
            MessageSegment.at(event.user_id),
            MessageSegment.text(f" \n 下载过程中发生错误，请稍后重试喵～")
        ])
        await bot.send(event, error_msg)

def extract_lofter_url(text: str) -> str:
    """提取Lofter链接"""
    pattern = r'https?://[\w-]+\.lofter\.com/post/[a-f0-9]+_[a-f0-9]+(?:\?[^\s]*)?'
    match = re.search(pattern, text)
    return match.group(0) if match else None

def clean_html(text: str) -> str:
    """清理HTML标签"""
    if not text:
        return ""
    text = html.unescape(text)
    text = re.sub(r'<[^>]+>', '', text)
    text = re.sub(r'\n\s*\n', '\n\n', text)
    return text.strip()

async def download_lofter_article(url: str):
    """下载Lofter文章"""
    try:
        # 解析URL
        url_pattern = r'https://([^.]+)\.lofter\.com/post/([^_]+)_([^?]+)'
        match = re.match(url_pattern, url)
        
        if not match:
            logger.error("链接格式错误")
            return None
        
        blog_name = match.group(1)
        blog_id_hex = match.group(2)
        
        try:
            blog_id = str(int(blog_id_hex, 16))
        except:
            logger.error("链接解析失败")
            return None
        
        logger.info(f"开始下载 {blog_name} 的文章...")
        
        # 设置请求头
        headers = {
            "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            LOFTER_COOKIE_KEY: LOFTER_COOKIE_VALUE
        }
        
        # 获取所有文章
        posts = await get_all_lofter_posts(blog_id, blog_name, headers)
        if not posts:
            logger.error("获取文章失败")
            return None
        
        logger.info(f"获取到 {len(posts)} 篇文章")
        
        # 查找目标文章
        post_id_hex = match.group(3)  # 从URL中提取文章ID
        target_post = find_target_article(posts, post_id_hex)
        if not target_post:
            logger.error("未找到匹配文章")
            return None
        
        # 提取内容
        content = extract_article_content(target_post)
        if not content:
            logger.error("提取内容失败")
            return None
        
        # 解析标题和作者
        lines = content.split('\n')
        title = "未知标题"
        author = "未知作者"
        
        for line in lines:
            if line.startswith('标题: '):
                title = line[3:].strip()
            elif line.startswith('作者: '):
                author = line[3:].strip()
        
        return title, content, author
        
    except Exception as e:
        logger.error(f"下载失败: {e}")
        return None

async def get_all_lofter_posts(blog_id: str, blog_name: str, headers: dict):
    """获取所有Lofter文章"""
    try:
        all_posts = []
        offset = 0
        limit = 500

        async with httpx.AsyncClient(timeout=30) as client:
            while True:
                api_url = "http://api.lofter.com/v2.0/blogHomePage.api?product=lofter-android-7.4.4"
                post_data = {
                    "targetblogid": blog_id,
                    "supportposttypes": "1,2,3,4,5,6",
                    "blogdomain": f"{blog_name}.lofter.com",
                    "offset": str(offset),
                    "method": "getPostLists",
                    "postdigestnew": "1",
                    "returnData": "1",
                    "limit": str(limit),
                    "checkpwd": "1",
                    "needgetpoststat": "1"
                }

                response = await client.post(api_url, data=post_data, headers=headers)

                if response.status_code != 200:
                    break

                try:
                    data = response.json()
                    posts = data['response']['posts']

                    if not posts:
                        break

                    all_posts.extend(posts)

                    if len(posts) < limit:
                        break

                    offset += limit
                except:
                    break

        return all_posts
    except Exception as e:
        logger.error(f"获取文章列表失败: {e}")
        return None

def find_target_article(posts: list, post_id_hex: str):
    """查找目标文章"""
    try:
        # 将16进制文章ID转换为10进制
        target_post_id = str(int(post_id_hex, 16))
        logger.info(f"查找文章ID: {target_post_id} (hex: {post_id_hex})")

        # 方法1: 通过文章ID精确匹配
        for post in posts:
            if post.get('post') and isinstance(post.get('post'), dict):
                nested_post = post.get('post')
                post_id = str(nested_post.get('id', ''))

                if post_id == target_post_id:
                    title = nested_post.get('title', nested_post.get('digest', '无标题'))
                    logger.info(f"通过ID找到文章: {title}")
                    return post

        # 方法2: 如果启用了关键词匹配，作为备选方案
        if ENABLE_KEYWORD_MATCHING and ARTICLE_KEYWORDS:
            logger.info("ID匹配失败，尝试关键词匹配...")
            best_match = None
            best_score = 0

            for post in posts:
                if post.get('post') and isinstance(post.get('post'), dict):
                    nested_post = post.get('post')
                    title = nested_post.get('title') or nested_post.get('digest')

                    if title:
                        title = clean_html(title).strip()
                        matched_keywords = [keyword for keyword in ARTICLE_KEYWORDS if keyword in title]
                        score = len(matched_keywords)

                        if score > best_score:
                            best_score = score
                            best_match = post

                        if score >= 3:  # 找到高匹配度文章就返回
                            logger.info(f"通过关键词找到文章: {title}")
                            return post

            if best_match:
                logger.info(f"选择最佳关键词匹配文章")
                return best_match

        # 方法3: 如果都没找到，返回第一篇文章作为默认选择
        if posts:
            first_post = posts[0]
            if first_post.get('post') and isinstance(first_post.get('post'), dict):
                title = first_post['post'].get('title', first_post['post'].get('digest', '无标题'))
                logger.info(f"未找到精确匹配，返回第一篇文章: {title}")
                return first_post

        logger.error("未找到任何可用文章")
        return None

    except Exception as e:
        logger.error(f"查找文章失败: {e}")
        return None

def extract_article_content(post: dict):
    """提取文章内容"""
    if post.get('post') and isinstance(post.get('post'), dict):
        actual_post = post.get('post')

        if actual_post.get('content'):
            title = actual_post.get('title', '无标题')
            author = actual_post.get('blogInfo', {}).get('blogNickName', '未知作者')
            content = actual_post.get('content')

            # 清理HTML
            content = clean_html(content)

            if content.strip():
                result = f"标题: {title}\n作者: {author}\n\n{'-'*50}\n\n{content}"
                return result

    return None

async def update_download_status(user_id: int, title: str, total_articles: int):
    """更新下载状态"""
    if user_id not in download_status:
        download_status[user_id] = []

    download_status[user_id].append({
        'title': title,
        'total_articles': total_articles,
        'current_article': 0,
        'progress': f"《{title}》 (0/{total_articles})"
    })

def clear_download_status(user_id: int, title: str):
    """清理下载状态"""
    if user_id in download_status:
        download_status[user_id] = [p for p in download_status[user_id] if p['title'] != title]
        if not download_status[user_id]:
            del download_status[user_id]

async def get_folder_id(bot: Bot, group_id: int, folder_name: str):
    """获取群文件夹ID"""
    try:
        jsonDate = await bot.call_api("get_group_root_files", group_id=group_id)
        for folder in jsonDate.get("folders", []):
            if folder_name == folder["folder_name"]:
                return folder["folder_id"]
        return await create_folder(bot, group_id, folder_name)
    except Exception as e:
        logger.error(f"获取群文件夹ID失败: {e}")
        return ""

async def create_folder(bot: Bot, group_id: int, folder_name: str):
    """创建群文件夹"""
    try:
        jsonDate = await bot.call_api("create_group_file_folder", group_id=group_id, folder_name=folder_name)
        return jsonDate["folder_id"]
    except Exception as e:
        logger.error(f"创建群文件夹失败: {e}")
        return ""

async def upload_to_webdav(file_path, file_name):
    """上传到WebDAV"""
    try:
        with open(file_path, 'rb') as f:
            file_content = f.read()

        remote_path = f"{WEBDAV_FOLDER}/{file_name}"
        webdav_url = f"{WEBDAV_URL}/{remote_path}"
        auth = (WEBDAV_USERNAME, WEBDAV_PASSWORD)

        async with httpx.AsyncClient(auth=auth, verify=False) as client:
            response = await client.put(webdav_url, content=file_content, timeout=60)

            if response.status_code in (200, 201, 204):
                base_url = WEBDAV_URL.split('/dav')[0]
                download_url = f"{base_url}/琪露诺上传的《小说》/{file_name}"
                logger.info(f"WebDAV上传成功: {download_url}")
                return download_url
            else:
                logger.error(f"WebDAV上传失败: {response.status_code}")
                return None
    except Exception as e:
        logger.error(f"WebDAV上传错误: {e}")
        return None
