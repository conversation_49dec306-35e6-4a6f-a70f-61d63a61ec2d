#!/usr/bin/env python3
# _*_ coding:utf-8 _*_

"""
notify包装脚本
用于让JavaScript脚本也能使用napcat推送功能
"""

import sys
import os
import requests

# 确保可以导入notify模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def napcat_push(title: str, content: str) -> None:
    """
    直接使用 napcat 推送消息到 QQ 群。
    """
    # napcat 配置
    napcat_url = os.getenv("NAPCAT_URL", "http://**************:7765/send_group_msg")
    napcat_group_id = os.getenv("NAPCAT_GROUP_ID", "1027740020")

    if not napcat_url or not napcat_group_id:
        print("napcat 服务的 NAPCAT_URL 或 NAPCAT_GROUP_ID 未设置!!")
        return

    print("napcat 服务启动")

    try:
        # 构建完整的 URL
        if napcat_url.endswith('/send_group_msg') or napcat_url.endswith('/send_msg'):
            url = napcat_url
        else:
            url = f'{napcat_url}/send_group_msg'

        group_id = int(napcat_group_id)

        # 构建推送内容（与SMTP邮件格式保持一致）
        message = f"{title}\n\n{content}"

        # 发送请求到 napcat
        response = requests.post(
            url,
            json={"group_id": group_id, "message": message},
            headers={"Content-Type": "application/json"},
            timeout=10
        )

        # 验证推送结果
        if response.status_code == 200:
            print("napcat QQ群推送成功！")
        else:
            print(f"napcat 推送失败，状态码：{response.status_code}，响应：{response.text}")
    except Exception as e:
        print(f"napcat 推送异常：{str(e)}")

def main():
    """主函数，处理命令行参数"""

    # 检查是否有命令行参数
    if len(sys.argv) >= 3:
        # 使用命令行参数
        title = sys.argv[1]
        content = sys.argv[2]
        print(f"接收到推送请求 - 标题: {title}")
    else:
        print("用法: python notify_wrapper.py <标题> <内容>")
        print("示例: python notify_wrapper.py \"顺丰速运\" \"账号登录成功...\"")
        sys.exit(1)

    try:
        # 调用我们自己的napcat推送函数
        print("=== 包装脚本推送开始 ===")
        print("============== 推送 ==============")
        napcat_push(title, content)
        print("=== 包装脚本推送完成 ===")

    except Exception as e:
        print(f"推送失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
