# Lofter文章下载QQ机器人插件

## 📋 功能介绍

这是一个基于NoneBot2的QQ机器人插件，可以自动识别群聊中的Lofter文章链接并下载文章内容。

### ✨ 主要功能

- 🔗 **自动识别Lofter链接** - 群聊中发送Lofter文章链接即可触发下载
- 📚 **智能文章匹配** - 自动查找目标文章（支持关键词匹配）
- 📁 **群文件上传** - 自动上传到群文件的"Lofter文章"文件夹
- 🌐 **WebDAV备份** - 群文件上传失败时自动上传到WebDAV
- 📊 **下载进度查询** - 支持查询当前下载进度
- 🎯 **@用户提醒** - 下载完成后@用户通知

## 🚀 安装配置

### 1. 文件部署

将以下文件放入您的NoneBot2插件目录：
- `lofter_bot_plugin.py` - 主插件文件
- `lofter_cookies_template.txt` - Cookie配置模板

### 2. Cookie配置

1. 复制 `lofter_cookies_template.txt` 为 `lofter_cookies.txt`
2. 在浏览器中登录 https://newsmiss.lofter.com
3. 按F12打开开发者工具，切换到Network标签页
4. 刷新页面，找到任意请求，复制Cookie头的值
5. 将Cookie值粘贴到 `lofter_cookies.txt` 文件中

### 3. 环境变量配置（可选）

如果需要WebDAV功能，请设置以下环境变量：

```bash
WEBDAV_URL=https://your-webdav-server.com/dav
WEBDAV_USERNAME=your_username
WEBDAV_PASSWORD=your_password
WEBDAV_FOLDER=Lofter文章
```

## 📖 使用方法

### 基本使用

1. **下载文章**：在群聊中发送Lofter文章链接
   ```
   https://dmscdj.lofter.com/post/84bc89fd_2be5a7548?incantation=rzD5Hc6Y0MGF
   ```

2. **查询进度**：发送"Lofter下载进度"或"lofter下载进度"

### 支持的链接格式

- `https://用户名.lofter.com/post/博客ID_文章ID`
- `https://用户名.lofter.com/post/博客ID_文章ID?参数`

### 示例对话

```
用户: https://dmscdj.lofter.com/post/84bc89fd_2be5a7548?incantation=rzD5Hc6Y0MGF

机器人: 稍候喵❤
       正在处理您的Lofter文章请求啦

机器人: @用户 
       《♂♂小鲛人被王爷亲手送进男风馆后，他学会了用残缺的尾巴讨好所有人》
       作者：村口做饭大厨
       ------------
       下载完毕了喵~
       已上传至群文件❤
       ------------
```

## 🔧 技术特性

### 智能匹配算法

插件使用关键词匹配算法来查找目标文章：
- 关键词：小鲛人、王爷、男风馆、残缺、尾巴、讨好
- 匹配度≥3个关键词时自动选择
- 支持HTML标签清理和内容格式化

### 文件上传策略

1. **优先群文件** - 自动创建"Lofter文章"文件夹并上传
2. **WebDAV备份** - 群文件失败时自动上传到WebDAV
3. **安全文件名** - 自动处理特殊字符，确保文件名安全

### 错误处理

- 网络超时重试
- Cookie失效提醒
- 详细错误日志
- 用户友好的错误提示

## 🛠️ 故障排除

### 常见问题

**Q1: 提示"未找到有效的Lofter Cookie"**
- 检查 `lofter_cookies.txt` 文件是否存在
- 确认Cookie格式正确，包含 `LOFTER-XXXXXX-LOGIN-AUTH` 字段
- 重新获取Cookie（可能已过期）

**Q2: 提示"未找到匹配文章"**
- 检查链接是否正确
- 确认文章是否需要特殊权限访问
- 文章可能已被删除或隐藏

**Q3: 群文件上传失败**
- 检查机器人是否有群文件上传权限
- 确认群文件存储空间是否充足
- 插件会自动尝试WebDAV备份

**Q4: WebDAV上传失败**
- 检查WebDAV服务器配置
- 确认用户名密码正确
- 检查网络连接

### 调试模式

查看机器人日志获取详细错误信息：
```bash
tail -f logs/nonebot.log | grep -i lofter
```

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 支持Lofter文章链接识别和下载
- 群文件自动上传功能
- WebDAV备份支持
- 下载进度查询

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个插件！

## 📄 许可证

本项目采用MIT许可证。
