#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Lofter下载器测试脚本
用于测试下载器的各个功能模块
"""

from lofter_downloader import LofterDownloader
import json


def test_cookie_loading():
    """测试Cookie加载功能"""
    print("=" * 50)
    print("测试Cookie加载功能")
    print("=" * 50)
    
    downloader = LofterDownloader()
    
    # 测试加载不存在的文件
    result = downloader.load_cookies("nonexistent.txt")
    print(f"加载不存在文件结果: {result}")
    
    # 测试加载现有文件
    result = downloader.load_cookies("cookies.txt")
    print(f"加载现有文件结果: {result}")
    
    return downloader if result else None


def test_search_author(downloader, author_name="村口做饭大厨"):
    """测试作者搜索功能"""
    print("=" * 50)
    print(f"测试作者搜索功能: {author_name}")
    print("=" * 50)
    
    if not downloader:
        print("❌ 下载器未初始化")
        return None
    
    result = downloader.search_author(author_name)
    
    if result:
        print(f"✅ 搜索成功")
        print(f"📊 结果数量: {len(result.get('blogs', []))}")
        
        for i, blog in enumerate(result.get('blogs', []), 1):
            print(f"{i}. {blog.get('blogNickName', '未知')} (@{blog.get('blogName', '未知')})")
            print(f"   博客ID: {blog.get('blogId')}")
            print(f"   简介: {blog.get('selfIntro', '无')[:50]}...")
        
        return result.get('blogs', [])
    else:
        print("❌ 搜索失败")
        return None


def test_get_posts(downloader, blog_info):
    """测试获取文章列表功能"""
    print("=" * 50)
    print("测试获取文章列表功能")
    print("=" * 50)
    
    if not downloader or not blog_info:
        print("❌ 参数无效")
        return None
    
    posts = downloader.get_author_posts(blog_info)
    
    if posts:
        print(f"✅ 获取文章列表成功")
        print(f"📊 文章数量: {len(posts)}")
        
        for i, post in enumerate(posts[:5], 1):  # 只显示前5篇
            title = post.get('title', post.get('digest', '无标题'))
            print(f"{i}. {title[:50]}...")
            print(f"   文章ID: {post.get('id')}")
            print(f"   博客ID: {post.get('blogId')}")
        
        if len(posts) > 5:
            print(f"... 还有 {len(posts) - 5} 篇文章")
        
        return posts
    else:
        print("❌ 获取文章列表失败")
        return None


def test_get_content(downloader, post_info):
    """测试获取文章内容功能"""
    print("=" * 50)
    print("测试获取文章内容功能")
    print("=" * 50)
    
    if not downloader or not post_info:
        print("❌ 参数无效")
        return None
    
    content = downloader.get_post_content(post_info)
    
    if content:
        print(f"✅ 获取文章内容成功")
        print(f"📊 内容长度: {len(content)} 字符")
        print("📖 内容预览:")
        print("-" * 30)
        print(content[:200] + "..." if len(content) > 200 else content)
        print("-" * 30)
        return content
    else:
        print("❌ 获取文章内容失败")
        return None


def test_save_article(downloader, title, content, author):
    """测试保存文章功能"""
    print("=" * 50)
    print("测试保存文章功能")
    print("=" * 50)
    
    if not downloader:
        print("❌ 下载器未初始化")
        return False
    
    try:
        downloader.save_article(title, content, author, "test_downloads")
        print("✅ 保存文章成功")
        return True
    except Exception as e:
        print(f"❌ 保存文章失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 Lofter下载器功能测试")
    print("=" * 60)
    
    # 1. 测试Cookie加载
    downloader = test_cookie_loading()
    if not downloader:
        print("❌ Cookie加载失败，无法继续测试")
        return
    
    # 2. 测试作者搜索
    author_name = input("\n请输入要测试的作者名 (默认: 村口做饭大厨): ").strip()
    if not author_name:
        author_name = "村口做饭大厨"
    
    blogs = test_search_author(downloader, author_name)
    if not blogs:
        print("❌ 作者搜索失败，无法继续测试")
        return
    
    # 选择第一个博客进行测试
    selected_blog = blogs[0]
    print(f"\n🎯 选择博客: {selected_blog.get('blogNickName')} 进行测试")
    
    # 3. 测试获取文章列表
    posts = test_get_posts(downloader, selected_blog)
    if not posts:
        print("❌ 获取文章列表失败，无法继续测试")
        return
    
    # 选择第一篇文章进行测试
    selected_post = posts[0]
    post_title = selected_post.get('title', selected_post.get('digest', '无标题'))
    print(f"\n🎯 选择文章: {post_title[:30]}... 进行测试")
    
    # 4. 测试获取文章内容
    content = test_get_content(downloader, selected_post)
    if not content:
        print("❌ 获取文章内容失败，无法继续测试")
        return
    
    # 5. 测试保存文章
    test_save_article(downloader, post_title, content, selected_blog.get('blogNickName', '未知作者'))
    
    print("\n" + "=" * 60)
    print("🎉 所有测试完成!")
    print("=" * 60)


if __name__ == "__main__":
    main()
