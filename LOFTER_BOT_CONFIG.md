# Lofter QQ机器人插件配置说明

## 🚀 快速配置

### 1. 获取Lofter Cookie

1. **登录Lofter**
   - 在浏览器中访问：https://newsmiss.lofter.com
   - 登录您的Lofter账号

2. **获取Cookie**
   - 按F12打开开发者工具
   - 切换到 Network（网络）标签页
   - 刷新页面
   - 找到任意一个请求，点击查看
   - 在 Request Headers 中找到 Cookie 行
   - 复制 Cookie 中的 `LOFTER-XXXXXX-LOGIN-AUTH` 部分

3. **示例Cookie格式**
   ```
   LOFTER-123456-LOGIN-AUTH=abcdef1234567890abcdef1234567890
   ```

### 2. 配置插件

打开 `lofter_bot_simple.py` 文件，找到配置区域（第19-21行）：

```python
# ==================== 配置区域 ====================
# 请在这里填入您的Lofter Cookie
LOFTER_COOKIE_KEY = ""  # 例如: "LOFTER-123456-LOGIN-AUTH"
LOFTER_COOKIE_VALUE = ""  # 例如: "your_cookie_value_here"
```

**填入您的Cookie信息**：
```python
LOFTER_COOKIE_KEY = "LOFTER-123456-LOGIN-AUTH"  # 替换为您的实际Cookie键
LOFTER_COOKIE_VALUE = "abcdef1234567890abcdef1234567890"  # 替换为您的实际Cookie值
```

### 3. 配置示例

```python
# ==================== 配置区域 ====================
# 请在这里填入您的Lofter Cookie
LOFTER_COOKIE_KEY = "LOFTER-987654-LOGIN-AUTH"
LOFTER_COOKIE_VALUE = "1a2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p7q8r9s0t"

# WebDAV配置（可选）
WEBDAV_URL = "https://al.zyii.xyz:666/dav"
WEBDAV_USERNAME = "yuedu"
WEBDAV_PASSWORD = "123456"
WEBDAV_FOLDER = "琪露诺上传的《小说》"

# 文章匹配关键词
ARTICLE_KEYWORDS = ['小鲛人', '王爷', '男风馆', '残缺', '尾巴', '讨好']
# ==================== 配置区域结束 ====================
```

## 🎯 使用方法

配置完成后，在群聊中发送Lofter文章链接即可：

```
https://dmscdj.lofter.com/post/84bc89fd_2be5a7548?incantation=rzD5Hc6Y0MGF
```

机器人会自动：
1. 识别链接
2. 下载文章
3. 上传到群文件
4. @您通知下载完成

## 🔧 高级配置

### 自定义关键词

如果要下载其他类型的文章，可以修改关键词：

```python
# 文章匹配关键词
ARTICLE_KEYWORDS = ['关键词1', '关键词2', '关键词3']
```

### WebDAV配置

如果群文件上传失败，插件会自动尝试上传到WebDAV：

```python
WEBDAV_URL = "https://your-webdav-server.com/dav"
WEBDAV_USERNAME = "your_username"
WEBDAV_PASSWORD = "your_password"
WEBDAV_FOLDER = "文章存储文件夹"
```

## ❓ 常见问题

### Q1: 提示"未配置Lofter Cookie"
- 检查 `LOFTER_COOKIE_KEY` 和 `LOFTER_COOKIE_VALUE` 是否都已填写
- 确保Cookie格式正确

### Q2: 提示"下载失败"
- Cookie可能已过期，重新获取
- 检查网络连接
- 确认文章链接正确

### Q3: 群文件上传失败
- 检查机器人是否有群文件上传权限
- 插件会自动尝试WebDAV备份

### Q4: 找不到匹配文章
- 检查关键词设置是否正确
- 确认文章标题包含设定的关键词

## 🔄 Cookie更新

当Cookie过期时，重新获取并更新配置：

1. 重新登录Lofter
2. 获取新的Cookie
3. 更新插件中的 `LOFTER_COOKIE_VALUE`
4. 重启机器人

## 📝 注意事项

1. **Cookie安全**：不要将Cookie分享给他人
2. **使用频率**：避免频繁下载，以免被限制
3. **版权尊重**：下载的文章仅供个人学习使用
4. **定期更新**：Cookie会定期过期，需要更新

## 🎉 完成

配置完成后，您的QQ机器人就可以自动下载Lofter文章了！

在群聊中发送Lofter链接，机器人会自动处理并上传到群文件。
