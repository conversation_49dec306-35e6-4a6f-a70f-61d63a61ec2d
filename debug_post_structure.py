#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试文章数据结构
"""

from lofter_downloader import LofterDownloader
import json


def debug_post_structure():
    """调试文章数据结构"""
    downloader = LofterDownloader()
    
    # 加载cookies
    if not downloader.load_cookies():
        print("❌ 无法加载Cookie，请先设置Cookie")
        return
    
    print("🔍 调试文章数据结构")
    print("=" * 50)
    
    # 直接调用API获取文章列表
    blog_id = "2226948605"
    blog_name = "dmscdj"
    
    api_url = "http://api.lofter.com/v2.0/blogHomePage.api?product=lofter-android-7.4.4"
    
    post_data = {
        "targetblogid": blog_id,
        "supportposttypes": "1,2,3,4,5,6",
        "blogdomain": f"{blog_name}.lofter.com",
        "offset": "0",
        "method": "getPostLists",
        "postdigestnew": "1",
        "returnData": "1",
        "limit": "5",  # 只获取5篇文章进行调试
        "checkpwd": "1",
        "needgetpoststat": "1"
    }
    
    print(f"📡 请求API: {api_url}")
    print(f"📋 请求数据: {post_data}")
    
    response = downloader.session.post(api_url, data=post_data, headers=downloader.headers)
    
    if response.status_code == 200:
        try:
            data = response.json()
            print(f"\n📊 响应状态: 成功")
            print(f"📋 响应结构: {list(data.keys())}")
            
            if 'response' in data and 'posts' in data['response']:
                posts = data['response']['posts']
                print(f"📚 文章数量: {len(posts)}")
                
                for i, post in enumerate(posts[:3], 1):  # 只显示前3篇
                    print(f"\n📖 第{i}篇文章的完整数据:")
                    print(json.dumps(post, indent=2, ensure_ascii=False))
                    print("-" * 50)
                    
                    # 尝试提取标题
                    title_candidates = [
                        post.get('title'),
                        post.get('digest'),
                        post.get('noticeLinkTitle'),
                        post.get('post', {}).get('title') if isinstance(post.get('post'), dict) else None,
                        post.get('post', {}).get('digest') if isinstance(post.get('post'), dict) else None,
                    ]
                    
                    print(f"🎯 标题候选:")
                    for j, candidate in enumerate(title_candidates, 1):
                        if candidate:
                            print(f"   {j}. {repr(candidate)[:100]}...")
                        else:
                            print(f"   {j}. None")
                    
                    # 选择最佳标题
                    best_title = None
                    for candidate in title_candidates:
                        if candidate and candidate.strip():
                            best_title = downloader.clean_html(candidate).strip()
                            if best_title and not best_title.isspace():
                                break
                    
                    print(f"✅ 最终标题: {repr(best_title)}")
                    print("=" * 50)
                    
            else:
                print("❌ 响应中没有找到posts数据")
                print(f"完整响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
        except json.JSONDecodeError:
            print("❌ 响应不是有效的JSON")
            print(f"响应内容: {response.text[:500]}...")
    else:
        print(f"❌ 请求失败，状态码: {response.status_code}")
        print(f"响应内容: {response.text[:500]}...")


if __name__ == "__main__":
    debug_post_structure()
