#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试脚本：检查API响应结构
"""

import requests
import json
from lofter_downloader import LofterDownloader


def debug_api_response():
    """调试API响应结构"""
    downloader = LofterDownloader()
    
    # 加载cookies
    if not downloader.load_cookies():
        print("❌ 无法加载Cookie，请先设置Cookie")
        return
    
    # 搜索作者
    print("🔍 搜索作者: 村口做饭大厨")
    search_result = downloader.search_author("村口做饭大厨")
    
    if not search_result or not search_result.get('blogs'):
        print("❌ 搜索失败")
        return
    
    # 选择第一个博客
    blog = search_result['blogs'][0]
    print(f"✅ 选择博客: {blog.get('blogNickName')}")
    
    # 获取文章列表的原始响应
    blog_id = blog.get('blogId')
    blog_name = blog.get('blogName')
    
    api_url = "http://api.lofter.com/v2.0/blogHomePage.api?product=lofter-android-7.4.4"
    
    post_data = {
        "targetblogid": blog_id,
        "supportposttypes": "1,2,3,4,5,6",
        "blogdomain": f"{blog_name}.lofter.com",
        "offset": "0",
        "method": "getPostLists",
        "postdigestnew": "1",
        "returnData": "1",
        "limit": "5",  # 只获取5篇文章进行调试
        "checkpwd": "1",
        "needgetpoststat": "1"
    }
    
    print(f"\n📡 请求API: {api_url}")
    print(f"📋 请求数据: {post_data}")
    
    response = downloader.session.post(api_url, data=post_data, headers=downloader.headers)
    
    if response.status_code == 200:
        try:
            data = response.json()
            print(f"\n📊 响应状态: 成功")
            print(f"📋 响应结构: {list(data.keys())}")
            
            if 'response' in data and 'posts' in data['response']:
                posts = data['response']['posts']
                print(f"📚 文章数量: {len(posts)}")
                
                if posts:
                    print(f"\n🔍 第一篇文章的完整数据结构:")
                    first_post = posts[0]
                    print(json.dumps(first_post, indent=2, ensure_ascii=False))
                    
                    print(f"\n📝 标题相关字段:")
                    title_fields = ['title', 'digest', 'noticeLinkTitle', 'content']
                    for field in title_fields:
                        if field in first_post:
                            value = first_post[field]
                            print(f"   {field}: {repr(value)}")
                        else:
                            print(f"   {field}: 不存在")
            else:
                print("❌ 响应中没有找到posts数据")
                print(f"完整响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
        except json.JSONDecodeError:
            print("❌ 响应不是有效的JSON")
            print(f"响应内容: {response.text[:500]}...")
    else:
        print(f"❌ 请求失败，状态码: {response.status_code}")
        print(f"响应内容: {response.text[:500]}...")


if __name__ == "__main__":
    debug_api_response()
