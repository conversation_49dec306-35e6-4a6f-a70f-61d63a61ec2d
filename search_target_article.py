#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门搜索目标文章
"""

from lofter_downloader import LofterDownloader


def search_target_article():
    """搜索包含小鲛人的文章"""
    downloader = LofterDownloader()
    
    # 加载cookies
    if not downloader.load_cookies():
        print("❌ 无法加载Cookie，请先设置Cookie")
        return
    
    print("🎯 搜索目标文章：小鲛人相关")
    print("=" * 50)
    
    # 博客信息
    blog_info = {
        'blogName': 'dmscdj',
        'blogId': '2226948605',
        'blogNickName': '村口做饭大厨'
    }
    
    print(f"👤 作者: {blog_info['blogNickName']}")
    
    # 获取更多文章
    print("📚 获取作者的全部文章...")
    posts = downloader.get_all_author_posts(blog_info)
    
    if not posts:
        print("❌ 无法获取文章列表")
        return
    
    print(f"✅ 获取到 {len(posts)} 篇文章")
    
    # 搜索目标关键词
    target_keywords = ['小鲛人', '王爷', '男风馆', '残缺', '尾巴', '讨好']
    
    print(f"\n🔍 在 {len(posts)} 篇文章中搜索关键词...")
    print(f"🎯 目标关键词: {target_keywords}")
    
    matches = []
    for i, post in enumerate(posts, 1):
        # 提取标题
        title = None
        if post.get('post') and isinstance(post.get('post'), dict):
            nested_post = post.get('post')
            title = nested_post.get('title') or nested_post.get('digest')
        
        if not title:
            title = post.get('title') or post.get('digest') or post.get('noticeLinkTitle')
        
        if title:
            title = downloader.clean_html(title).strip()
            
            # 检查是否包含关键词
            matched_keywords = [keyword for keyword in target_keywords if keyword in title]
            
            if matched_keywords:
                matches.append((i, post, title, matched_keywords))
                print(f"\n🎯 匹配 #{i}: {title}")
                print(f"   匹配关键词: {matched_keywords}")
                
                # 如果匹配度很高，显示更多信息
                if len(matched_keywords) >= 2:
                    print(f"   ⭐ 高匹配度文章!")
                    if post.get('post'):
                        post_id = post['post'].get('id')
                        permalink = post['post'].get('permalink')
                        print(f"   文章ID: {post_id}")
                        print(f"   永久链接: {permalink}")
    
    print(f"\n📊 搜索结果:")
    print(f"   总文章数: {len(posts)}")
    print(f"   匹配文章数: {len(matches)}")
    
    if matches:
        print(f"\n🎯 找到 {len(matches)} 篇匹配的文章:")
        for i, (article_num, post, title, keywords) in enumerate(matches, 1):
            print(f"{i}. 第{article_num}篇: {title[:50]}...")
            print(f"   关键词: {keywords}")
        
        # 选择最佳匹配
        best_match = max(matches, key=lambda x: len(x[3]))
        _, best_post, best_title, best_keywords = best_match
        
        print(f"\n✅ 最佳匹配: {best_title}")
        print(f"   匹配关键词: {best_keywords}")
        
        # 尝试下载
        print(f"\n📖 尝试下载最佳匹配文章...")
        content = downloader.get_post_content(best_post)
        
        if content:
            print("✅ 下载成功!")
            print(f"📊 内容长度: {len(content)} 字符")
            
            # 保存文章
            downloader.save_article(best_title, content, blog_info['blogNickName'], "target_downloads")
            print("💾 文章已保存到 target_downloads 目录")
        else:
            print("❌ 下载失败")
    else:
        print("❌ 未找到包含目标关键词的文章")
        print("💡 可能的原因:")
        print("   - 文章标题与预期不符")
        print("   - 文章可能已被删除或隐藏")
        print("   - 需要检查其他关键词")


if __name__ == "__main__":
    search_target_article()
