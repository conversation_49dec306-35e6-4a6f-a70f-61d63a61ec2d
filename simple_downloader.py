#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Lofter文章下载器
快速、简洁、无冗余输出
"""

import requests
import json
import os
import re
from urllib.parse import quote
import html


class SimpleLofterDownloader:
    def __init__(self):
        self.session = requests.Session()
        self.cookies = {}
        self.headers = {
            "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
    def load_cookies(self, cookie_file: str = "cookies.txt") -> bool:
        """加载cookies"""
        try:
            if os.path.exists(cookie_file):
                with open(cookie_file, 'r', encoding='utf-8') as f:
                    cookie_str = f.read().strip()
                    
                # 跳过注释行
                lines = cookie_str.split('\n')
                cookie_str = ""
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        cookie_str = line
                        break
                
                if not cookie_str:
                    return False
                    
                # 解析cookie
                for cookie in cookie_str.split(';'):
                    if '=' in cookie:
                        key, value = cookie.strip().split('=', 1)
                        self.cookies[key] = value
                        
                # 查找认证cookie
                for key, value in self.cookies.items():
                    if key.startswith('LOFTER-') and key.endswith('-LOGIN-AUTH'):
                        self.headers.update({key: value})
                        self.session.cookies.update(self.cookies)
                        return True
                        
            return False
        except:
            return False
    
    def clean_html(self, text: str) -> str:
        """清理HTML标签"""
        if not text:
            return ""
        text = html.unescape(text)
        text = re.sub(r'<[^>]+>', '', text)
        text = re.sub(r'\n\s*\n', '\n\n', text)
        return text.strip()
    
    def get_all_posts(self, blog_id: str, blog_name: str) -> list:
        """快速获取所有文章"""
        all_posts = []
        offset = 0
        limit = 500
        
        while True:
            api_url = "http://api.lofter.com/v2.0/blogHomePage.api?product=lofter-android-7.4.4"
            post_data = {
                "targetblogid": blog_id,
                "supportposttypes": "1,2,3,4,5,6",
                "blogdomain": f"{blog_name}.lofter.com",
                "offset": str(offset),
                "method": "getPostLists",
                "postdigestnew": "1",
                "returnData": "1",
                "limit": str(limit),
                "checkpwd": "1",
                "needgetpoststat": "1"
            }
            
            response = self.session.post(api_url, data=post_data, headers=self.headers)
            
            if response.status_code != 200:
                break
                
            try:
                data = response.json()
                posts = data['response']['posts']
                
                if not posts:
                    break
                    
                all_posts.extend(posts)
                
                if len(posts) < limit:
                    break
                    
                offset += limit
            except:
                break
        
        return all_posts
    
    def find_article(self, posts: list, keywords: list) -> dict:
        """查找匹配的文章"""
        best_match = None
        best_score = 0
        
        for post in posts:
            title = None
            if post.get('post') and isinstance(post.get('post'), dict):
                nested_post = post.get('post')
                title = nested_post.get('title') or nested_post.get('digest')
            
            if title:
                title = self.clean_html(title).strip()
                matched_keywords = [keyword for keyword in keywords if keyword in title]
                score = len(matched_keywords)
                
                if score > best_score:
                    best_score = score
                    best_match = post
                    
                if score >= 3:  # 找到高匹配度文章就返回
                    return post
        
        return best_match
    
    def extract_content(self, post: dict) -> str:
        """提取文章内容"""
        if post.get('post') and isinstance(post.get('post'), dict):
            actual_post = post.get('post')
            
            if actual_post.get('content'):
                title = actual_post.get('title', '无标题')
                author = actual_post.get('blogInfo', {}).get('blogNickName', '未知作者')
                content = actual_post.get('content')
                
                # 清理HTML
                content = self.clean_html(content)
                
                if content.strip():
                    result = f"标题: {title}\n作者: {author}\n\n{'-'*50}\n\n{content}"
                    return result
        
        return None
    
    def save_article(self, title: str, content: str, author: str):
        """保存文章"""
        if not os.path.exists("downloads"):
            os.makedirs("downloads")
        
        safe_title = re.sub(r'[<>:"/\\|?*]', '_', title)
        safe_author = re.sub(r'[<>:"/\\|?*]', '_', author)
        
        filename = f"{safe_author}_{safe_title}.txt"
        filepath = os.path.join("downloads", filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return filepath
    
    def download_from_url(self, url: str) -> bool:
        """通过链接下载文章"""
        # 解析URL
        url_pattern = r'https://([^.]+)\.lofter\.com/post/([^_]+)_([^?]+)'
        match = re.match(url_pattern, url)
        
        if not match:
            print("❌ 链接格式错误")
            return False
        
        blog_name = match.group(1)
        blog_id_hex = match.group(2)
        
        try:
            blog_id = str(int(blog_id_hex, 16))
        except:
            print("❌ 链接解析失败")
            return False
        
        print(f"📚 获取 {blog_name} 的文章...")
        
        # 获取所有文章
        posts = self.get_all_posts(blog_id, blog_name)
        if not posts:
            print("❌ 获取文章失败")
            return False
        
        print(f"✅ 获取到 {len(posts)} 篇文章")
        
        # 查找目标文章
        keywords = ['小鲛人', '王爷', '男风馆', '残缺', '尾巴', '讨好']
        target_post = self.find_article(posts, keywords)
        
        if not target_post:
            print("❌ 未找到匹配文章")
            return False
        
        # 提取内容
        content = self.extract_content(target_post)
        if not content:
            print("❌ 提取内容失败")
            return False
        
        # 保存文章
        lines = content.split('\n')
        title = "未知标题"
        author = "未知作者"
        
        for line in lines:
            if line.startswith('标题: '):
                title = line[3:].strip()
            elif line.startswith('作者: '):
                author = line[3:].strip()
        
        filepath = self.save_article(title, content, author)
        print(f"✅ 文章已保存: {filepath}")
        
        return True


def main():
    """主程序"""
    downloader = SimpleLofterDownloader()
    
    print("🌟 简化版Lofter下载器")
    print("=" * 40)
    
    # 加载cookies
    if not downloader.load_cookies():
        print("❌ 请先设置cookies.txt文件")
        return
    
    print("✅ Cookie加载成功")
    
    # 输入链接
    url = input("请输入文章链接: ").strip()
    if not url:
        print("❌ 链接不能为空")
        return
    
    # 下载文章
    if downloader.download_from_url(url):
        print("🎉 下载完成!")
    else:
        print("❌ 下载失败")


if __name__ == "__main__":
    main()
